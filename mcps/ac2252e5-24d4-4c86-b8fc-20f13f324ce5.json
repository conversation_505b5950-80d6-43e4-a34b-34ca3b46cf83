{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "ac2252e5-24d4-4c86-b8fc-20f13f324ce5", "name": "Eraser", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/eraser-icon-logo-png_seeklogo-483569.png/1756713912-eraser-icon-logo-png_seeklogo-483569.png", "description": "Generate professional diagrams from text descriptions using the Eraser API through a simple MCP interface.", "category": "general", "tags": null, "created_at": "2025-09-01T08:05:18.412972", "updated_at": "2025-09-01T08:06:02.615753", "owner_id": "2051be58-0123-407f-892f-cbe74966f0ab", "hosted_url": "https://server.smithery.ai/@dravidsajinraj-iex/ai-diagram-generator-mcp/mcp?api_key=ccf813d1-f1f1-49d1-923f-265d462eb4ac&profile=genuine-worm-wAjvKW", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON><PERSON> <PERSON>h", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["generateDiagram"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "generateDiagram", "description": "Generate a diagram from text description using Eraser API", "input_schema": {"type": "object", "properties": {"prompt": {"type": "string", "description": "Text description of the diagram to generate"}}, "required": ["prompt"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {"type": "object", "properties": {"message": {"type": "string", "description": "A status or success message indicating the diagram generation result."}, "diagramUrl": {"type": "string", "format": "uri", "description": "The URL of the generated diagram image."}, "promptUsed": {"type": "string", "description": "The prompt or parameters used to generate the diagram."}}, "required": ["message", "diagramUrl", "promptUsed"]}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": null, "url": null}}