{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "4fd76366-bd5b-4f10-bd23-276b4875ec67", "name": "Cal.com Testing Only", "logo": null, "description": null, "category": "general", "tags": null, "created_at": "2025-07-22T11:30:33.637197", "updated_at": "2025-07-23T08:38:54.121541", "owner_id": "985d0f11-2fb1-4f16-bfa5-babb0689bbfa", "hosted_url": "https://cal-mcp-dev-no-auth-624209391722.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["list_bookings", "create_booking", "list_event_types", "check_availability"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "list_bookings", "description": "Get all Cal.com bookings for a specified time range. Returns a concise list with essential booking information.", "input_schema": {"description": "Schema for listing Cal.com bookings", "properties": {"api_key": {"description": "Cal.com API key for authentication", "title": "Api Key", "type": "string"}, "limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 100, "description": "Maximum number of bookings to return", "title": "Limit"}, "status": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Filter by booking status (confirmed, cancelled, etc.)", "title": "Status"}, "days_back": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 30, "description": "Number of days back to search", "title": "Days Back"}, "days_forward": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 30, "description": "Number of days forward to search", "title": "Days Forward"}}, "required": ["api_key"], "title": "ListBookings", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "create_booking", "description": "Create a new booking in Cal.com. Requires event type ID, start time, attendee details, and timezone.", "input_schema": {"description": "Schema for creating a new Cal.com booking", "properties": {"api_key": {"description": "Cal.com API key for authentication", "title": "Api Key", "type": "string"}, "event_type_id": {"description": "Cal.com Event Type ID (get this from list_event_types)", "title": "Event Type Id", "type": "integer"}, "start_time": {"description": "Start time in ISO format (e.g., '2024-01-15T14:00:00')", "title": "Start Time", "type": "string"}, "attendee_name": {"description": "Full name of the attendee", "title": "Attendee Name", "type": "string"}, "attendee_email": {"description": "Email address of the attendee", "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "timezone": {"description": "Timezone (e.g., 'America/New_York', 'UTC')", "title": "Timezone", "type": "string"}}, "required": ["api_key", "event_type_id", "start_time", "attendee_name", "attendee_email", "timezone"], "title": "CreateBooking", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "list_event_types", "description": "Get all available Cal.com event types with their IDs, names, and durations", "input_schema": {"description": "Schema for listing Cal.com event types", "properties": {"api_key": {"description": "Cal.com API key for authentication", "title": "Api Key", "type": "string"}}, "required": ["api_key"], "title": "ListEventTypes", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "check_availability", "description": "Check available time slots for a Cal.com event type on a specific date", "input_schema": {"description": "Schema for checking availability for a Cal.com event type", "properties": {"api_key": {"description": "Cal.com API key for authentication", "title": "Api Key", "type": "string"}, "event_type_id": {"description": "Cal.com Event Type ID (get this from list_event_types)", "title": "Event Type Id", "type": "integer"}, "date": {"description": "Date to check in YYYY-MM-DD format (e.g., '2024-01-15')", "title": "Date", "type": "string"}, "timezone": {"description": "Timezone (e.g., 'America/New_York', 'Asia/Calcutta')", "title": "Timezone", "type": "string"}}, "required": ["api_key", "event_type_id", "date", "timezone"], "title": "CheckAvailability", "type": "object"}, "output_schema": null, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": null, "url": null}}