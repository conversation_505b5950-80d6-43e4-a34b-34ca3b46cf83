{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "cb33feb8-1d13-4b54-aa0f-ff11ae7efbd2", "name": "Flux-image", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/flux-ai-image-generator-icon-filled-256.png/1756726446-flux-ai-image-generator-icon-filled-256.png", "description": "ImageGen MCP Server is a streamlined server implementation that provides powerful image generation capabilities through the Model Context Protocol (MCP).", "category": "general", "tags": null, "created_at": "2025-09-01T11:34:13.909374", "updated_at": "2025-09-01T11:41:02.926418", "owner_id": "2051be58-0123-407f-892f-cbe74966f0ab", "hosted_url": "https://server.smithery.ai/@falahgs/flux-imagegen-mcp-server/mcp?api_key=ccf813d1-f1f1-49d1-923f-265d462eb4ac&profile=genuine-worm-wAjvKW", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON><PERSON> <PERSON>h", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["generateImage", "listImageModels", "generateImageUrl"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "generateImage", "description": "Generate an image, return the base64-encoded data, and save to a file by default", "input_schema": {"type": "object", "properties": {"prompt": {"type": "string", "description": "The text description of the image to generate"}, "model": {"type": "string", "description": "Model name to use for generation (default: \"flux\"). Available options: \"flux, \"turbo\" (sdxl),"}, "seed": {"type": "number", "description": "Seed for reproducible results (default: random)"}, "width": {"type": "number", "description": "Width of the generated image (default: 1024)"}, "height": {"type": "number", "description": "Height of the generated image (default: 1024)"}, "enhance": {"type": "boolean", "description": "Whether to enhance the prompt using an LLM before generating (default: true)"}, "safe": {"type": "boolean", "description": "Whether to apply content filtering (default: false)"}, "outputPath": {"type": "string", "description": "Directory path where to save the image (default: \"./mcpollinations-output\")"}, "fileName": {"type": "string", "description": "Name of the file to save (without extension, default: generated from prompt)"}, "format": {"type": "string", "description": "Image format to save as (png, jpeg, jpg, webp - default: png)"}}, "required": ["prompt"]}, "output_schema": {"properties": {"test": {"type": "string", "description": "aohfojsgovk", "title": "test"}}}, "annotations": null}, {"name": "listImageModels", "description": "List available image models", "input_schema": {"type": "object", "properties": {}}, "output_schema": {"properties": {"hjgjgj": {"type": "string", "description": "hjhjghg", "title": "hjgjgj"}}}, "annotations": null}, {"name": "generateImageUrl", "description": "Generate an image URL from a text prompt", "input_schema": {"type": "object", "properties": {"prompt": {"type": "string", "description": "The text description of the image to generate"}, "model": {"type": "string", "description": "Model name to use for generation (default: \"flux\"). Available options: \"flux\", \"sdxl\", \"sd3\", \"sd15\", \"flux-schnell\", \"flux-dev\""}, "seed": {"type": "number", "description": "Seed for reproducible results (default: random)"}, "width": {"type": "number", "description": "Width of the generated image (default: 1024)"}, "height": {"type": "number", "description": "Height of the generated image (default: 1024)"}, "enhance": {"type": "boolean", "description": "Whether to enhance the prompt using an LLM before generating (default: true)"}, "safe": {"type": "boolean", "description": "Whether to apply content filtering (default: false)"}}, "required": ["prompt"]}, "output_schema": {"properties": {"imageUrl": {"type": "string", "description": "URL of the Image Generated", "title": "imageUrl"}, "prompt": {"type": "string", "description": "The prompt given to generate image", "title": "prompt"}, "width": {"type": "number", "description": "Width specification for the image", "title": "width"}, "height": {"type": "number", "description": "Height specification for the image", "title": "height"}, "model": {"type": "string", "description": "Model specification for image generation", "title": "model"}, "seed": {"type": "number", "description": "random number", "title": "seed"}, "enhance": {"type": "boolean", "description": "Enhance the image generated or not", "title": "enhance"}, "private": {"type": "boolean", "description": "want image to be private or not", "title": "private"}, "nologo": {"type": "boolean", "description": "nil", "title": "nologo"}, "safe": {"type": "boolean", "description": "safe image generation or not", "title": "safe"}}}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": null, "url": null}}