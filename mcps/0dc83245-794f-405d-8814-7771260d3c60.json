{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "0dc83245-794f-405d-8814-7771260d3c60", "name": "Script Generation", "logo": null, "description": "This is Script Generation MCP Server", "category": "general", "tags": null, "created_at": "2025-06-12T15:07:25.756492", "updated_at": "2025-07-25T12:21:58.170822", "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07", "hosted_url": "https://script-generation-mcp-dev-624209391722.us-central1.run.app/sse", "mcp_type": "sse", "owner_name": "<PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["script_generate", "research"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "script_generate", "description": "Provide topic and keyword to generator <PERSON><PERSON><PERSON>", "input_schema": {"$defs": {"Keywords": {"properties": {"time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Time"}, "objective": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Objective"}, "audience": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Audience"}, "gender": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Gender"}, "tone": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON>"}, "speakers": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Speakers"}}, "title": "Keywords", "type": "object"}, "ScriptType": {"enum": ["TOPIC", "SCRIPT", "BLOG", "AI"], "title": "ScriptType", "type": "string"}, "VideoType": {"enum": ["SHORT", "LONG"], "title": "VideoType", "type": "string"}}, "properties": {"topic": {"title": "Topic", "type": "string"}, "script_type": {"anyOf": [{"$ref": "#/$defs/ScriptType"}, {"type": "null"}], "default": null}, "keywords": {"$ref": "#/$defs/Keywords"}, "video_type": {"$ref": "#/$defs/VideoType", "default": "SHORT"}, "link": {"anyOf": [{"format": "uri", "maxLength": 2083, "minLength": 1, "type": "string"}, {"type": "null"}], "default": null, "title": "Link"}}, "required": ["topic"], "title": "GenerateScriptInput", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "research", "description": "Research for the given topic", "input_schema": {"properties": {"topic": {"title": "Topic", "type": "string"}}, "required": ["topic"], "title": "ResearchInput", "type": "object"}, "output_schema": null, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": null, "url": null}}