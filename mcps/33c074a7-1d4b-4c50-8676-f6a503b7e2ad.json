{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "33c074a7-1d4b-4c50-8676-f6a503b7e2ad", "name": "Flux ImageGen", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/flux.png/1750839598-flux.png", "description": "ImageGen MCP Server is a streamlined server implementation that provides powerful image generation capabilities through the Model Context Protocol (MCP). ", "category": "general", "tags": null, "created_at": "2025-06-12T15:04:17.588332", "updated_at": "2025-08-26T06:52:47.973327", "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07", "hosted_url": null, "mcp_type": "stdio", "owner_name": "<PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": "https://github.com/ShrutiAgrawal-Rapid/flux-imagegen-mcp-server", "api_documentation": null, "capabilities": ["generateImageUrl", "generateImage", "listImageModels"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "generateImageUrl", "description": "Generate an image URL from a text prompt", "input_schema": {"type": "object", "properties": {"prompt": {"type": "string", "description": "The text description of the image to generate"}, "model": {"type": "string", "description": "Model name to use for generation (default: \"flux\"). Available options: \"flux\", \"sdxl\", \"sd3\", \"sd15\", \"flux-schnell\", \"flux-dev\""}, "seed": {"type": "number", "description": "Seed for reproducible results (default: random)"}, "width": {"type": "number", "description": "Width of the generated image (default: 1024)"}, "height": {"type": "number", "description": "Height of the generated image (default: 1024)"}, "enhance": {"type": "boolean", "description": "Whether to enhance the prompt using an LLM before generating (default: true)"}, "safe": {"type": "boolean", "description": "Whether to apply content filtering (default: false)"}}, "required": ["prompt"]}, "output_schema": {"properties": {"imageUrl": {"type": "string", "description": "URL of the Image Generated", "title": "imageUrl"}, "prompt": {"type": "string", "description": "The prompt given to generate image", "title": "prompt"}, "width": {"type": "number", "description": "Width specification for the image", "title": "width"}, "height": {"type": "number", "description": "Height specification for the image", "title": "height"}, "model": {"type": "string", "description": "Model specification for image generation", "title": "model"}, "seed": {"type": "number", "description": "random number", "title": "seed"}, "enhance": {"type": "boolean", "description": "Enhance the image generated or not", "title": "enhance"}, "private": {"type": "boolean", "description": "want image to be private or not", "title": "private"}, "nologo": {"type": "boolean", "description": "nil", "title": "nologo"}, "safe": {"type": "boolean", "description": "safe image generation or not", "title": "safe"}}}, "annotations": null}, {"name": "generateImage", "description": "Generate an image, return the base64-encoded data, and save to a file by default", "input_schema": {"type": "object", "properties": {"prompt": {"type": "string", "description": "The text description of the image to generate"}, "model": {"type": "string", "description": "Model name to use for generation (default: \"flux\"). Available options: \"flux, \"turbo\" (sdxl),"}, "seed": {"type": "number", "description": "Seed for reproducible results (default: random)"}, "width": {"type": "number", "description": "Width of the generated image (default: 1024)"}, "height": {"type": "number", "description": "Height of the generated image (default: 1024)"}, "enhance": {"type": "boolean", "description": "Whether to enhance the prompt using an LLM before generating (default: true)"}, "safe": {"type": "boolean", "description": "Whether to apply content filtering (default: false)"}, "outputPath": {"type": "string", "description": "Directory path where to save the image (default: \"./mcpollinations-output\")"}, "fileName": {"type": "string", "description": "Name of the file to save (without extension, default: generated from prompt)"}, "format": {"type": "string", "description": "Image format to save as (png, jpeg, jpg, webp - default: png)"}}, "required": ["prompt"]}, "output_schema": {"properties": {"test": {"type": "string", "description": "aohfojsgovk", "title": "test"}}}, "annotations": null}, {"name": "listImageModels", "description": "List available image models", "input_schema": {"type": "object", "properties": {}}, "output_schema": {"properties": {"hjgjgj": {"type": "string", "description": "hjhjghg", "title": "hjgjgj"}}}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": "flux-imagegen-mcp-server", "git_user_name": "ShrutiAgrawal-Rapid", "integrations": null, "url": null}}