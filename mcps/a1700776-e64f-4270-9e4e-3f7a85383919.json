{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "a1700776-e64f-4270-9e4e-3f7a85383919", "name": "script generation", "logo": null, "description": "generate the video script", "category": "marketing", "tags": ["script", "video"], "created_at": "2025-06-13T05:30:24.531333", "updated_at": "2025-07-14T11:42:37.361221", "owner_id": "fce79072-a235-4127-ac5b-b5b1709a8077", "hosted_url": "https://script-generation-mcp-dev-624209391722.us-central1.run.app/sse", "mcp_type": "sse", "owner_name": "<PERSON><PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["research", "script_generate"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "research", "description": "Research for the given topic", "input_schema": {"properties": {"topic": {"title": "Topic", "type": "string"}}, "required": ["topic"], "title": "ResearchInput", "type": "object"}, "output_schema": {"properties": {"test": {"type": "string", "description": "oejfojglt", "title": "test"}}}, "annotations": null}, {"name": "script_generate", "description": "Provide topic and keyword to generator <PERSON><PERSON><PERSON>", "input_schema": {"$defs": {"Keywords": {"properties": {"time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Time"}, "objective": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Objective"}, "audience": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Audience"}, "gender": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Gender"}, "tone": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON>"}, "speakers": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Speakers"}}, "title": "Keywords", "type": "object"}, "ScriptType": {"enum": ["VIDEO", "TOPIC", "SCRIPT", "BLOG", "AI"], "title": "ScriptType", "type": "string"}, "VideoType": {"enum": ["SHORT", "LONG"], "title": "VideoType", "type": "string"}}, "properties": {"topic": {"title": "Topic", "type": "string"}, "script_type": {"$ref": "#/$defs/ScriptType", "default": "TOPIC"}, "keywords": {"$ref": "#/$defs/Keywords"}, "video_type": {"$ref": "#/$defs/VideoType", "default": "SHORT"}, "link": {"anyOf": [{"format": "uri", "maxLength": 2083, "minLength": 1, "type": "string"}, {"type": "null"}], "default": null, "title": "Link"}}, "required": ["topic"], "title": "GenerateScriptInput", "type": "object"}, "output_schema": {"properties": {"title": {"type": "string", "description": "Title of the generated script", "title": "Title"}, "script": {"type": "string", "description": "The generated script", "title": "<PERSON><PERSON><PERSON>"}, "script_type": {"type": "string", "description": "Type of the script", "title": "Script Type"}, "video_type": {"type": "string", "description": "The type of video", "title": "Video Type"}, "link": {"type": "string", "format": "uri", "description": "Optional link for the script", "title": "Link"}}}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": null, "url": null}}