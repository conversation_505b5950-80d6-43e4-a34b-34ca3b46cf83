{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "d00cfd0e-67ec-4dbd-9a9b-fcec170fd2b4", "name": "mcp-fetch", "logo": null, "description": "This tool is designed for macOS only due to its dependency on macOS-specific clipboard operations", "category": "general", "tags": ["fetch", "test"], "created_at": "2025-06-12T14:52:15.981180", "updated_at": "2025-08-04T05:16:42.552716", "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07", "hosted_url": null, "mcp_type": "stdio", "owner_name": "<PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": "https://github.com/smithery-ai/mcp-fetch", "api_documentation": null, "capabilities": ["fetch"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "fetch", "description": "Retrieves URLs from the Internet and extracts their content as markdown. If images are found, their URLs will be included in the response.", "input_schema": {"type": "object", "properties": {"url": {"type": "string", "format": "uri"}, "maxLength": {"type": "number", "exclusiveMinimum": 0, "maximum": 1000000, "default": 20000}, "startIndex": {"type": "number", "minimum": 0, "default": 0}, "raw": {"type": "boolean", "default": false}}, "required": ["url"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {"properties": {"test": {"type": "string", "description": "test", "title": "test"}}}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": "mcp-fetch", "git_user_name": "smithery-ai", "integrations": null, "url": null}}