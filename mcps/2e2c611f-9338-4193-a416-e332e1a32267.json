{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "2e2c611f-9338-4193-a416-e332e1a32267", "name": "Apollo IO", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/image_2025-08-12_192853695.png/1755007134-image_2025-08-12_192853695.png", "description": "MCP server for utilizing Apollo IO's RESTFUL APIs", "category": "sales", "tags": ["sales", "apolloio", "apollo"], "created_at": "2025-08-12T13:59:10.893429", "updated_at": "2025-08-25T09:48:28.418616", "owner_id": "14902289-d9a4-43ec-8a31-989babf03fe3", "hosted_url": "https://apollo-mcp-dev-624209391722.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON><PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["bulk_people_enrichment", "organization_enrichment", "people_search", "organization_search", "organization_job_postings", "get_person_email", "employees_of_company", "people_enrichment"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "bulk_people_enrichment", "description": "Use the Bulk People Enrichment endpoint to enrich data for up to 10 people in a single request. Supports revealing personal emails and phone numbers.", "input_schema": {"type": "object", "properties": {"details": {"type": "array", "description": "Array of up to 10 people to enrich", "maxItems": 10, "minItems": 1, "items": {"type": "object", "properties": {"first_name": {"type": "string", "description": "Person's first name"}, "last_name": {"type": "string", "description": "Person's last name"}, "name": {"type": "string", "description": "Full name (first and last name)"}, "email": {"type": "string", "description": "Person's email address"}, "hashed_email": {"type": "string", "description": "MD5 or SHA-256 hashed email"}, "organization": {"type": "object", "description": "Organization details", "properties": {"name": {"type": "string", "description": "Organization name"}, "domain": {"type": "string", "description": "Organization domain"}}}, "linkedin_url": {"type": "string", "description": "Person's LinkedIn profile URL"}, "id": {"type": "string", "description": "Apollo ID for the person"}}}}, "reveal_personal_emails": {"type": "boolean", "description": "Set to true to reveal personal emails for all matched people. Consumes credits. Not available for GDPR-compliant regions. Defaults to false."}, "reveal_phone_number": {"type": "boolean", "description": "Set to true to reveal phone numbers for all matched people. Consumes credits. Requires webhook_url. Defaults to false."}, "webhook_url": {"type": "string", "description": "Mandatory if reveal_phone_number is true. URL where <PERSON> sends asynchronous phone number verification responses."}}, "required": ["details"]}, "output_schema": {}, "annotations": null}, {"name": "organization_enrichment", "description": "Use the Organization Enrichment endpoint to enrich data for 1 company", "input_schema": {"type": "object", "properties": {"domain": {"type": "string", "description": "Company domain"}, "name": {"type": "string", "description": "Company name"}}}, "output_schema": null, "annotations": null}, {"name": "people_search", "description": "Use the People Search endpoint to find people", "input_schema": {"type": "object", "properties": {"q_organization_domains_list": {"type": "array", "items": {"type": "string"}, "description": "List of organization domains to search within"}, "organization_ids": {"type": "array", "items": {"type": "string"}, "description": "List of Apollo organization IDs to search within"}, "organization_locations": {"type": "array", "items": {"type": "string"}, "description": "List of organization headquarters locations (e.g., \"California, US\", \"New York, US\")"}, "organization_num_employees_ranges": {"type": "array", "items": {"type": "string"}, "description": "List of employee count ranges (e.g., \"1,10\", \"11,50\", \"51,200\", \"201,500\", \"501,1000\", \"1001,5000\", \"5001,10000\", \"10001+\")"}, "q_organization_name": {"type": "string", "description": "Organization name to search for"}, "person_titles": {"type": "array", "items": {"type": "string"}, "description": "List of job titles to search for"}, "person_seniorities": {"type": "array", "items": {"type": "string"}, "description": "List of seniority levels (e.g., \"c_suite\", \"vp\", \"director\", \"manager\", \"senior\", \"entry\")"}, "person_locations": {"type": "array", "items": {"type": "string"}, "description": "List of personal locations where people are based (e.g., \"California, US\", \"London, UK\")"}, "person_departments": {"type": "array", "items": {"type": "string"}, "description": "List of departments (e.g., \"sales\", \"marketing\", \"engineering\", \"finance\", \"hr\")"}, "person_functions": {"type": "array", "items": {"type": "string"}, "description": "List of functional areas (e.g., \"sales\", \"marketing\", \"engineering\", \"operations\")"}, "person_keywords": {"type": "array", "items": {"type": "string"}, "description": "Keywords to search for in person profiles"}, "contact_email_status": {"type": "array", "items": {"type": "string"}, "description": "Email verification status (e.g., \"verified\", \"guessed\", \"unavailable\", \"bounced\")"}, "reveal_personal_emails": {"type": "boolean", "description": "Set to true to reveal personal emails. Consumes credits. Defaults to false."}, "reveal_phone_number": {"type": "boolean", "description": "Set to true to reveal phone numbers. Consumes credits. Defaults to false."}, "include_emails": {"type": "boolean", "description": "Set to true to include email addresses in results. Defaults to false."}, "page": {"type": "number", "description": "Page number for pagination (starts from 1). Defaults to 1."}, "per_page": {"type": "number", "description": "Number of results per page (1-100). Defaults to 25."}, "sort_by_field": {"type": "string", "description": "Field to sort by (e.g., \"name\", \"title\", \"company\")"}, "sort_ascending": {"type": "boolean", "description": "Sort direction. True for ascending, false for descending. Defaults to true."}, "exclude_person_ids": {"type": "array", "items": {"type": "string"}, "description": "List of Apollo person IDs to exclude from results"}}}, "output_schema": {}, "annotations": null}, {"name": "organization_search", "description": "Use the Organization Search endpoint to find organizations", "input_schema": {"type": "object", "properties": {"q_organization_domains_list": {"type": "array", "items": {"type": "string"}, "description": "List of organization domains to search for"}, "organization_locations": {"type": "array", "items": {"type": "string"}, "description": "List of organization locations to search for"}}}, "output_schema": null, "annotations": null}, {"name": "organization_job_postings", "description": "Use the Organization Job Postings endpoint to find job postings for a specific organization", "input_schema": {"type": "object", "properties": {"organization_id": {"type": "string", "description": "Apollo.io organization ID"}}, "required": ["organization_id"]}, "output_schema": null, "annotations": null}, {"name": "get_person_email", "description": "Get email address for a person using their Apollo ID", "input_schema": {"type": "object", "properties": {"apollo_id": {"type": "string", "description": "Apollo.io person ID"}}, "required": ["apollo_id"]}, "output_schema": null, "annotations": null}, {"name": "employees_of_company", "description": "Find employees of a company using company name or website/LinkedIn URL", "input_schema": {"type": "object", "properties": {"company": {"type": "string", "description": "Company name"}, "website_url": {"type": "string", "description": "Company website URL"}, "linkedin_url": {"type": "string", "description": "Company LinkedIn URL"}}, "required": ["company"]}, "output_schema": null, "annotations": null}, {"name": "people_enrichment", "description": "Use the People Enrichment endpoint to enrich data for 1 person. Supports revealing personal emails and phone numbers.", "input_schema": {"type": "object", "properties": {"first_name": {"type": "string", "description": "Person's first name"}, "last_name": {"type": "string", "description": "Person's last name"}, "name": {"type": "string", "description": "Full name (first and last name). If used, first_name and last_name are not needed."}, "email": {"type": "string", "description": "Person's email address"}, "hashed_email": {"type": "string", "description": "MD5 or SHA-256 hashed email of the person"}, "domain": {"type": "string", "description": "Company domain (do not include www. or @)"}, "organization_name": {"type": "string", "description": "Organization name"}, "id": {"type": "string", "description": "Apollo ID for the person"}, "linkedin_url": {"type": "string", "description": "Person's LinkedIn profile URL"}, "reveal_personal_emails": {"type": "boolean", "description": "Set to true to reveal personal emails. Consumes credits. Not available for GDPR-compliant regions. Defaults to false."}, "reveal_phone_number": {"type": "boolean", "description": "Set to true to reveal phone numbers. Consumes credits. Requires webhook_url. Defaults to false."}, "webhook_url": {"type": "string", "description": "Mandatory if reveal_phone_number is true. URL where <PERSON> sends asynchronous phone number verification responses."}}}, "output_schema": {}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": null, "url": null}}