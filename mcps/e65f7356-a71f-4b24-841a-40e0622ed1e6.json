{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "e65f7356-a71f-4b24-841a-40e0622ed1e6", "name": "<PERSON><PERSON><PERSON>", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/twilio_inc__logo.jpeg/1755060819-twilio_inc__logo.jpeg", "description": null, "category": "general", "tags": null, "created_at": "2025-08-13T04:53:45.566293", "updated_at": "2025-08-15T08:19:38.329957", "owner_id": "5229e05b-aaea-4850-9972-7268559318cf", "hosted_url": "https://twilio-mcp-dev-624209391722.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["send_sms"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "send_sms", "description": "Send sms to the user using Twilio", "input_schema": {"properties": {"to": {"title": "To", "type": "string"}, "body": {"title": "Body", "type": "string"}, "from_phone_number": {"title": "From Phone Number", "type": "string"}}, "required": ["to", "body", "from_phone_number"], "title": "SendSMS", "type": "object"}, "output_schema": null, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": null, "url": null}}