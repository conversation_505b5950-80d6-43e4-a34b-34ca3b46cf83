{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "0730b975-db31-4861-87c1-216ac6c3c907", "name": "Slack MCP", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/images.png/1751446056-images.png", "description": "This is a connector to allow <PERSON> (or any MCP client) to interact with your Slack workspace to post messages and query a list of all users.", "category": "general", "tags": null, "created_at": "2025-07-02T08:42:16.907512", "updated_at": "2025-08-29T06:07:18.680140", "owner_id": "8510e2f9-15de-4024-9378-7f863a59cc6e", "hosted_url": "https://slack-mcp-dev-624209391722.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["get_message", "list_channels", "add_reaction", "get_thread_replies", "get_user_profile", "get_conversation", "create_channel", "create_private_channel", "find_public_channel", "set_channel_topic", "get_message_permalink", "edit_message", "delete_message", "find_message", "get_message_by_timestamp", "retrieve_thread_messages", "find_user_by_email", "find_user_by_id", "find_user_by_name", "update_profile", "set_status", "send_channel_message", "send_direct_message", "send_private_channel_message", "invite_user_to_channel", "remove_user_from_channel", "add_reminder"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "get_message", "description": "Get a specific message by timestamp", "input_schema": {"properties": {"channel_id": {"description": "The ID of the channel containing the message", "title": "Channel Id", "type": "string"}, "timestamp": {"description": "The timestamp of the message to retrieve", "title": "Timestamp", "type": "string"}}, "required": ["channel_id", "timestamp"], "title": "GetMessage", "type": "object"}, "output_schema": {"properties": {"sucess": {"type": "string", "description": "sucess", "title": "sucess"}, "failure": {"type": "string", "description": "falied", "title": "failure"}}}, "annotations": null}, {"name": "list_channels", "description": "List public or pre-defined channels in the workspace with pagination", "input_schema": {"properties": {"limit": {"anyOf": [{"maximum": 200, "minimum": 1, "type": "integer"}, {"type": "null"}], "default": 100, "description": "Maximum number of channels to return (max 200)", "title": "Limit", "type": "integer"}, "cursor": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Pagination cursor for next page of results", "title": "<PERSON><PERSON><PERSON>", "type": "string"}}, "title": "ListChannels", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "add_reaction", "description": "Add a reaction emoji to a message", "input_schema": {"properties": {"channel_id": {"description": "The ID of the channel containing the message", "title": "Channel Id", "type": "string"}, "timestamp": {"description": "The timestamp of the message to react to", "title": "Timestamp", "type": "string"}, "reaction": {"description": "The name of the emoji reaction (without ::)", "title": "Reaction", "type": "string"}}, "required": ["channel_id", "timestamp", "reaction"], "title": "AddReaction", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_thread_replies", "description": "Get all replies in a message thread", "input_schema": {"properties": {"channel_id": {"description": "The ID of the channel containing the thread", "title": "Channel Id", "type": "string"}, "thread_ts": {"description": "The timestamp of the parent message", "title": "<PERSON><PERSON><PERSON> Ts", "type": "string"}}, "required": ["channel_id", "thread_ts"], "title": "GetThreadReplies", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_user_profile", "description": "Get detailed profile information for a specific user", "input_schema": {"properties": {"user_id": {"description": "The ID of the user to retrieve profile information for", "title": "User Id", "type": "string"}}, "required": ["user_id"], "title": "GetUserProfile", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_conversation", "description": "Get information about a conversation/channel", "input_schema": {"properties": {"channel_id": {"description": "The ID of the channel/conversation to retrieve information for", "title": "Channel Id", "type": "string"}}, "required": ["channel_id"], "title": "GetConversation", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "create_channel", "description": "Create a new public channel", "input_schema": {"properties": {"name": {"description": "Name of the channel to create (without #)", "title": "Name", "type": "string"}, "is_private": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "description": "Whether the channel should be private (default: false)", "title": "Is Private", "type": "boolean"}}, "required": ["name"], "title": "CreateChannel", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "create_private_channel", "description": "Create a new private channel", "input_schema": {"properties": {"name": {"description": "Name of the private channel to create (without #)", "title": "Name", "type": "string"}}, "required": ["name"], "title": "CreatePrivateChannel", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "find_public_channel", "description": "Find a public channel by name", "input_schema": {"properties": {"name": {"description": "Name of the public channel to find (without #)", "title": "Name", "type": "string"}}, "required": ["name"], "title": "FindPublicChannel", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "set_channel_topic", "description": "Set the topic for a channel", "input_schema": {"properties": {"channel_id": {"description": "The ID of the channel to set the topic for", "title": "Channel Id", "type": "string"}, "topic": {"description": "The new topic text for the channel", "title": "Topic", "type": "string"}}, "required": ["channel_id", "topic"], "title": "SetChannelTopic", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_message_permalink", "description": "Get a permalink URL for a specific message", "input_schema": {"properties": {"channel_id": {"description": "The ID of the channel containing the message", "title": "Channel Id", "type": "string"}, "message_ts": {"description": "The timestamp of the message to get permalink for", "title": "Message Ts", "type": "string"}}, "required": ["channel_id", "message_ts"], "title": "GetMessagePermalink", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "edit_message", "description": "Update/edit a message", "input_schema": {"properties": {"channel_id": {"description": "The ID of the channel containing the message", "title": "Channel Id", "type": "string"}, "timestamp": {"description": "The timestamp of the message to edit", "title": "Timestamp", "type": "string"}, "text": {"description": "The new text content for the message", "title": "Text", "type": "string"}}, "required": ["channel_id", "timestamp", "text"], "title": "EditMessage", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "delete_message", "description": "Delete a message", "input_schema": {"properties": {"channel_id": {"description": "The ID of the channel containing the message", "title": "Channel Id", "type": "string"}, "timestamp": {"description": "The timestamp of the message to delete", "title": "Timestamp", "type": "string"}}, "required": ["channel_id", "timestamp"], "title": "DeleteMessage", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "find_message", "description": "Search for messages matching a query", "input_schema": {"properties": {"query": {"description": "Search query to find matching messages", "title": "Query", "type": "string"}, "count": {"anyOf": [{"maximum": 100, "minimum": 1, "type": "integer"}, {"type": "null"}], "default": 20, "description": "Number of search results to return", "title": "Count", "type": "integer"}}, "required": ["query"], "title": "FindMessage", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_message_by_timestamp", "description": "Get a message by its timestamp", "input_schema": {"properties": {"channel_id": {"description": "The ID of the channel containing the message", "title": "Channel Id", "type": "string"}, "timestamp": {"description": "The timestamp of the message to retrieve", "title": "Timestamp", "type": "string"}}, "required": ["channel_id", "timestamp"], "title": "GetMessageByTimestamp", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "retrieve_thread_messages", "description": "Retrieve all messages in a thread", "input_schema": {"properties": {"channel_id": {"description": "The ID of the channel containing the thread", "title": "Channel Id", "type": "string"}, "thread_ts": {"description": "The timestamp of the parent message of the thread", "title": "<PERSON><PERSON><PERSON> Ts", "type": "string"}}, "required": ["channel_id", "thread_ts"], "title": "RetrieveThreadMessages", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "find_user_by_email", "description": "Find a user by their email address", "input_schema": {"properties": {"email": {"description": "Email address of the user to find", "title": "Email", "type": "string"}}, "required": ["email"], "title": "FindUserByEmail", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "find_user_by_id", "description": "Find a user by their ID", "input_schema": {"properties": {"user_id": {"description": "The ID of the user to find", "title": "User Id", "type": "string"}}, "required": ["user_id"], "title": "FindUserById", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "find_user_by_name", "description": "Find a user by their name or username", "input_schema": {"properties": {"name": {"description": "Name or username of the user to find", "title": "Name", "type": "string"}}, "required": ["name"], "title": "FindUserByName", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "update_profile", "description": "Update user profile", "input_schema": {"properties": {"profile_data": {"additionalProperties": {"type": "string"}, "description": "Dictionary containing profile fields to update (e.g., {'status_text': 'Working', 'display_name': '<PERSON>'})", "title": "Profile Data", "type": "object"}}, "required": ["profile_data"], "title": "UpdateProfile", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "set_status", "description": "Set user status", "input_schema": {"properties": {"status_text": {"description": "Status text to display in your profile", "title": "Status Text", "type": "string"}, "status_emoji": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "", "description": "Status emoji to display with the status text (e.g., ':coffee:')", "title": "Status Emoji", "type": "string"}, "expiration": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "Unix timestamp for when the status should expire", "title": "Expiration", "type": "integer"}}, "required": ["status_text"], "title": "SetStatus", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "send_channel_message", "description": "Send a message to a channel", "input_schema": {"properties": {"channel_id": {"description": "The ID of the channel to send the message to", "title": "Channel Id", "type": "string"}, "text": {"description": "The message text content to send", "title": "Text", "type": "string"}, "thread_ts": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Timestamp of parent message to reply to in a thread", "title": "<PERSON><PERSON><PERSON> Ts", "type": "string"}, "as_user": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "description": "Send the message as the authenticated user (default: True)", "title": "As User", "type": "boolean"}}, "required": ["channel_id", "text"], "title": "SendChannelMessage", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "send_direct_message", "description": "Send a direct message to a user", "input_schema": {"properties": {"user_id": {"description": "The ID of the user to send the direct message to", "title": "User Id", "type": "string"}, "text": {"description": "The message text content to send", "title": "Text", "type": "string"}, "as_user": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "description": "Send the message as the authenticated user (default: True)", "title": "As User", "type": "boolean"}}, "required": ["user_id", "text"], "title": "SendDirectMessage", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "send_private_channel_message", "description": "Send a message to a private channel", "input_schema": {"properties": {"channel_id": {"description": "The ID of the private channel to send the message to", "title": "Channel Id", "type": "string"}, "text": {"description": "The message text content to send", "title": "Text", "type": "string"}, "thread_ts": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Timestamp of parent message to reply to in a thread", "title": "<PERSON><PERSON><PERSON> Ts", "type": "string"}, "as_user": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "description": "Send the message as the authenticated user (default: True)", "title": "As User", "type": "boolean"}}, "required": ["channel_id", "text"], "title": "SendPrivateChannelMessage", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "invite_user_to_channel", "description": "Invite a user to a channel", "input_schema": {"properties": {"channel_id": {"description": "The ID of the channel to invite the user to", "title": "Channel Id", "type": "string"}, "user_id": {"description": "The ID of the user to invite to the channel", "title": "User Id", "type": "string"}}, "required": ["channel_id", "user_id"], "title": "InviteUserToChannel", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "remove_user_from_channel", "description": "Remove a user from a channel", "input_schema": {"properties": {"channel_id": {"description": "The ID of the channel to remove the user from", "title": "Channel Id", "type": "string"}, "user_id": {"description": "The ID of the user to remove from the channel", "title": "User Id", "type": "string"}}, "required": ["channel_id", "user_id"], "title": "RemoveUserFromChannel", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "add_reminder", "description": "Add a reminder", "input_schema": {"properties": {"text": {"description": "The content/description of the reminder", "title": "Text", "type": "string"}, "time": {"description": "When to send the reminder (Unix timestamp or natural language like 'in 10 minutes')", "title": "Time", "type": "string"}, "user": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "User ID to remind (defaults to current user if not specified)", "title": "User", "type": "string"}}, "required": ["text", "time"], "title": "AddReminder", "type": "object"}, "output_schema": null, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": ["f9c18e00-5d78-44ed-9348-2bade5dde681"], "url": null}}