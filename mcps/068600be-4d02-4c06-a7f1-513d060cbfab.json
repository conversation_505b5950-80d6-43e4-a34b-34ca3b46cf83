{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "068600be-4d02-4c06-a7f1-513d060cbfab", "name": "voice-generation-mcp", "logo": null, "description": "generate audio from text", "category": "marketing", "tags": ["voice", "audio"], "created_at": "2025-06-15T10:40:44.141408", "updated_at": "2025-08-04T05:16:40.251994", "owner_id": "fce79072-a235-4127-ac5b-b5b1709a8077", "hosted_url": "https://voice-generation-mcp-dev-624209391722.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["create_voices", "generate_audio", "fetch_audio"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "create_voices", "description": "Add", "input_schema": {"type": "object", "properties": {"text": {"type": "string"}, "voice_id": {"type": "string"}}, "required": ["text", "voice_id"]}, "output_schema": {"properties": {"test": {"type": "string", "description": "voice", "title": "test"}}}, "annotations": null}, {"name": "generate_audio", "description": "Generate video audio using the script", "input_schema": {"$defs": {"VoiceProvider": {"enum": ["elevenlabs", "playht"], "title": "VoiceProvider", "type": "string"}}, "properties": {"script": {"description": "Script is required", "maxLength": 10000, "minLength": 1, "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "voice_id": {"maxLength": 50, "minLength": 1, "title": "Voice Id", "type": "string"}, "provider": {"$ref": "#/$defs/VoiceProvider", "default": "elevenlabs", "description": "Optional voice provider platform"}}, "required": ["script", "voice_id"], "title": "GenerateAudio", "type": "object"}, "output_schema": {"type": "object", "properties": {"audio_ids": {"type": "array", "description": "audio ids received from Eleven labs", "title": "audio_ids"}, "voice_id": {"type": "string", "description": "voice id", "title": "voice_id"}, "audio_script": {"type": "string", "description": "audio script", "title": "audio_script"}}, "required": ["audio_ids", "voice_id", "audio_script"]}, "annotations": null}, {"name": "fetch_audio", "description": "Fetch audio generated files links using ids", "input_schema": {"$defs": {"VoiceProvider": {"enum": ["elevenlabs", "playht"], "title": "VoiceProvider", "type": "string"}}, "properties": {"audio_ids": {"description": "List of voice IDs is required", "items": {"type": "string"}, "minItems": 1, "title": "Audio Ids", "type": "array"}, "provider": {"$ref": "#/$defs/VoiceProvider", "default": "elevenlabs", "description": "Optional voice provider platform"}}, "required": ["audio_ids"], "title": "FetchGenerateAudio", "type": "object"}, "output_schema": {"type": "object", "properties": {"audio_urls": {"type": "array", "description": "Urls of the Audio", "title": "audio_urls"}, "mimetype": {"type": "string", "description": "Mimetype of the audio", "title": "mimetype"}}, "required": ["audio_urls", "mimetype"]}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": null, "url": null}}