{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "cde76df3-a879-496a-95f4-8b1f95d81a12", "name": "stock-image-generation-mcp", "logo": null, "description": "generate image and a stock image ", "category": "marketing", "tags": ["image", "stock-image"], "created_at": "2025-06-16T04:05:28.737966", "updated_at": "2025-08-31T04:56:53.750056", "owner_id": "fce79072-a235-4127-ac5b-b5b1709a8077", "hosted_url": "https://stock-image-generation-mcp-dev-624209391722.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["generate_stock_image", "generate_ai_stock_image", "generate_image", "fetch_stock_images"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "generate_stock_image", "description": "generate and find the stock image for the video", "input_schema": {"properties": {"script": {"description": "Script is required", "maxLength": 1000, "minLength": 1, "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "view_type": {"maxLength": 50, "minLength": 1, "title": "View Type", "type": "string"}}, "required": ["script", "view_type"], "title": "GenerateStockImage", "type": "object"}, "output_schema": {"properties": {"stock_image_clips": {"type": "array", "description": "List of stock video clips", "items": {"type": "object", "properties": {"at_time": {"type": "number", "description": "Time at which the image clip starts", "title": "at_time"}, "url": {"type": "string", "description": "URL of the image", "title": "url"}, "prompt": {"type": "string", "description": "prompt for the image", "title": "prompt"}, "mimetype": {"type": "string", "description": "mimetype of the image clip", "title": "mimetype"}}}, "title": "stock_image_clips"}}}, "annotations": null}, {"name": "generate_ai_stock_image", "description": "generate and find the stock image for the video", "input_schema": {"properties": {"script": {"description": "Script is required", "maxLength": 1000, "minLength": 1, "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "view_type": {"maxLength": 50, "minLength": 1, "title": "View Type", "type": "string"}}, "required": ["script", "view_type"], "title": "GenerateAIStockImage", "type": "object"}, "output_schema": {"type": "object", "properties": {"stock_image_clips": {"type": "array", "description": "stock_image_clips", "title": "stock_image_clips"}}, "required": ["stock_image_clips"]}, "annotations": null}, {"name": "generate_image", "description": "generate the image using the script", "input_schema": {"properties": {"prompt": {"description": "Script is required", "maxLength": 1000, "minLength": 1, "title": "Prompt", "type": "string"}, "view_type": {"maxLength": 50, "minLength": 1, "title": "View Type", "type": "string"}}, "required": ["prompt", "view_type"], "title": "GenerateImage", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "fetch_stock_images", "description": "fetch the stock image using the script", "input_schema": {"properties": {"script": {"description": "Script is required", "maxLength": 1000, "minLength": 1, "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "view_type": {"maxLength": 50, "minLength": 1, "title": "View Type", "type": "string"}}, "required": ["script", "view_type"], "title": "GenerateAIStockImage", "type": "object"}, "output_schema": null, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": null, "url": null}}