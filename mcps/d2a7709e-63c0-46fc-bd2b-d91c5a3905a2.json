{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "d2a7709e-63c0-46fc-bd2b-d91c5a3905a2", "name": "SlideSpeak", "logo": null, "description": "SlideSpeak MCP Server responsible to create powerpoint presentations.", "category": "marketing", "tags": null, "created_at": "2025-06-26T07:18:50.862053", "updated_at": "2025-07-23T05:19:34.991011", "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07", "hosted_url": "https://slidespeak-mcp-dev-624209391722.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["generate_powerpoint", "get_available_templates", "generate_powerpoint_slide_by_slide"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "generate_powerpoint", "description": "Generate a PowerPoint presentation based on text, length, and template using SlideSpeak", "input_schema": {"properties": {"plain_text": {"title": "Plain Text", "type": "string"}, "length": {"title": "Length", "type": "integer"}, "template": {"title": "Template", "type": "string"}}, "required": ["plain_text", "length", "template"], "title": "GeneratePowerpoint", "type": "object"}, "output_schema": {"properties": {"Output": {"type": "string", "description": "Output", "title": "Output"}}}, "annotations": null}, {"name": "get_available_templates", "description": "Get all available presentation templates from SlideSpeak", "input_schema": {"properties": {"limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Limit"}}, "title": "GetAvailableTemplates", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "generate_powerpoint_slide_by_slide", "description": "Generate a PowerPoint presentation slide by slide based on slides array and template using SlideSpeak", "input_schema": {"properties": {"slides": {"items": {"additionalProperties": true, "type": "object"}, "title": "Slides", "type": "array"}, "template": {"title": "Template", "type": "string"}}, "required": ["slides", "template"], "title": "GeneratePowerpointSlideBySlide", "type": "object"}, "output_schema": null, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": null, "url": null}}