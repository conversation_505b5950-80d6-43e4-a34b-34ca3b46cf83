{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "1397e70d-e094-41bf-ad85-25b11a17f062", "name": "Google Drive", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/Google_Drive.png/1750857244-Google_Drive.png", "description": "Google Drive MCP Server to interact with the google drive.", "category": "general", "tags": null, "created_at": "2025-06-25T13:14:15.134882", "updated_at": "2025-08-09T16:18:57.349587", "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07", "hosted_url": "https://google-drive-mcp-dev-624209391722.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["find_file", "delete_file", "create_file"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "find_file", "description": "Find files in Google Drive based on search query", "input_schema": {"properties": {"query": {"title": "Query", "type": "string"}, "max_results": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 10, "title": "Max Results"}}, "required": ["query"], "title": "FindFile", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "delete_file", "description": "Delete a file from Google Drive by its ID", "input_schema": {"properties": {"file_id": {"title": "File Id", "type": "string"}}, "required": ["file_id"], "title": "DeleteFile", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "create_file", "description": "Create a new file in Google Drive with optional content", "input_schema": {"properties": {"name": {"title": "Name", "type": "string"}, "content": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Content"}, "mime_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "text/plain", "title": "Mime Type"}, "parent_folder_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Parent Folder Id"}}, "required": ["name"], "title": "CreateFile", "type": "object"}, "output_schema": {}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": ["9a7d4a23-9b96-45bb-976b-9db61e9a5dc9"], "url": null}}