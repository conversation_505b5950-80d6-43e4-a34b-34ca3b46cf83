{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "56dfe8af-e982-4351-a669-0a03755b8c99", "name": "video-generation-mcp", "logo": null, "description": "video generation mcp server", "category": "marketing", "tags": ["video"], "created_at": "2025-06-15T13:32:40.223075", "updated_at": "2025-08-27T13:13:11.089419", "owner_id": "fce79072-a235-4127-ac5b-b5b1709a8077", "hosted_url": "https://video-generation-mcp-dev-624209391722.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["generate_video"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "generate_video", "description": "generate and process the video", "input_schema": {"$defs": {"EventStockClip": {"properties": {"clip": {"minimum": 0, "title": "Clip", "type": "integer"}, "at_time": {"minimum": 0.0, "title": "At Time", "type": "number"}, "duration": {"exclusiveMinimum": 0.0, "title": "Duration", "type": "number"}}, "required": ["clip", "at_time", "duration"], "title": "EventStockClip", "type": "object"}, "StockImageClip": {"properties": {"at_time": {"minimum": 0.0, "title": "At Time", "type": "number"}, "url": {"format": "uri", "maxLength": 2083, "minLength": 1, "title": "Url", "type": "string"}}, "required": ["at_time", "url"], "title": "StockImageClip", "type": "object"}, "StockVideoClip": {"properties": {"at_time": {"minimum": 0.0, "title": "At Time", "type": "number"}, "url": {"format": "uri", "maxLength": 2083, "minLength": 1, "title": "Url", "type": "string"}}, "required": ["at_time", "url"], "title": "StockVideoClip", "type": "object"}, "VideoViewType": {"enum": ["LANDSCAPE", "PORTRAIT", "SQUARE"], "title": "VideoViewType", "type": "string"}}, "properties": {"view_type": {"$ref": "#/$defs/VideoViewType"}, "stock_video_clips": {"default": [], "items": {"$ref": "#/$defs/StockVideoClip"}, "title": "Stock Video Clips", "type": "array"}, "stock_image_clips": {"default": [], "items": {"$ref": "#/$defs/StockImageClip"}, "title": "Stock Image Clips", "type": "array"}, "event_stock_clips": {"default": [], "items": {"$ref": "#/$defs/EventStockClip"}, "title": "Event Stock Clips", "type": "array"}, "audio_urls": {"items": {"format": "uri", "maxLength": 2083, "minLength": 1, "type": "string"}, "title": "Audio Urls", "type": "array"}, "avatar_video_urls": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Avatar Video Urls"}, "subtitles": {"minLength": 1, "title": "Subtitles", "type": "string"}}, "required": ["view_type", "audio_urls", "subtitles"], "title": "GenerateVideoObject", "type": "object"}, "output_schema": {"properties": {"thumbnail": {"type": "object", "properties": {"url": {"type": "string", "description": "URL of the thumbnail", "title": "url"}, "mimetype": {"type": "string", "description": "MIME type of the thumbnail", "title": "mimetype"}}, "title": "thumbnail"}, "video_link": {"type": "object", "properties": {"url": {"type": "string", "description": "URL of the video", "title": "url"}, "mimetype": {"type": "string", "description": "MIME type of the video", "title": "mimetype"}}, "title": "video_link"}, "duration": {"type": "number", "description": "Duration of the video", "title": "duration"}}}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": null, "url": null}}