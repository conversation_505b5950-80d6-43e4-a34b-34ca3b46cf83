{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "d94e4d15-7459-4969-91a9-bbe3aeef08e6", "name": "Desktop Commander", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/desktop%20commander.jpeg/1750839632-desktopcommander.jpeg", "description": "This is MCP server for Claude that gives it terminal control, file system search and diff file editing capabilities", "category": "general", "tags": null, "created_at": "2025-06-12T14:44:28.654741", "updated_at": "2025-08-15T08:20:19.786291", "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07", "hosted_url": null, "mcp_type": "stdio", "owner_name": "<PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": "https://github.com/wonderwhy-er/DesktopCommanderMCP", "api_documentation": null, "capabilities": ["get_config", "read_file", "read_multiple_files", "move_file", "get_file_info", "execute_command", "read_output", "force_terminate", "list_sessions", "list_processes", "kill_process", "create_directory", "write_file", "edit_block", "set_config_value", "list_directory", "search_files", "search_code"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "get_config", "description": "\n                        Get the complete server configuration as JSON. Config includes fields for:\n                        - blockedCommands (array of blocked shell commands)\n                        - defaultShell (shell to use for commands)\n                        - allowedDirectories (paths the server can access)\n                        - fileReadLineLimit (max lines for read_file, default 1000)\n                        - fileWriteLineLimit (max lines per write_file call, default 50)\n                        - telemetryEnabled (boolean for telemetry opt-in/out)\n                        -  version (version of the DesktopCommander)\n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions.", "input_schema": {"type": "object", "properties": {}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {"properties": {"hjbbj": {"type": "string", "description": " jjjhj<PERSON>h", "title": "hjbbj"}}}, "annotations": null}, {"name": "read_file", "description": "\n                        Read the contents of a file from the file system or a URL with optional offset and length parameters.\n                        \n                        Prefer this over 'execute_command' with cat/type for viewing files.\n                        \n                        Supports partial file reading with:\n                        - 'offset' (start line, default: 0)\n                          * Positive: Start from line N (0-based indexing)\n                          * Negative: Read last N lines from end (tail behavior)\n                        - 'length' (max lines to read, default: configurable via 'fileReadLineLimit' setting, initially 1000)\n                          * Used with positive offsets for range reading\n                          * Ignored when offset is negative (reads all requested tail lines)\n                        \n                        Examples:\n                        - offset: 0, length: 10     → First 10 lines\n                        - offset: 100, length: 5    → Lines 100-104\n                        - offset: -20               → Last 20 lines  \n                        - offset: -5, length: 10    → Last 5 lines (length ignored)\n                        \n                        Performance optimizations:\n                        - Large files with negative offsets use reverse reading for efficiency\n                        - Large files with deep positive offsets use byte estimation\n                        - Small files use fast readline streaming\n                        \n                        When reading from the file system, only works within allowed directories.\n                        Can fetch content from URLs when isUrl parameter is set to true\n                        (URLs are always read in full regardless of offset/length).\n                        \n                        Handles text files normally and image files are returned as viewable images.\n                        Recognized image types: PNG, JPEG, GIF, WebP.\n                        \n                        IMPORTANT: Always use absolute paths (starting with '/' or drive letter like 'C:\\') for reliability. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.\n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions.", "input_schema": {"type": "object", "properties": {"path": {"type": "string"}, "isUrl": {"type": "boolean", "default": false}, "offset": {"type": "number", "default": 0}, "length": {"type": "number", "default": 1000}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {"properties": {"jhbbh": {"type": "string", "description": "jhvvh", "title": "jhbbh"}}}, "annotations": null}, {"name": "read_multiple_files", "description": "\n                        Read the contents of multiple files simultaneously.\n                        \n                        Each file's content is returned with its path as a reference.\n                        Handles text files normally and renders images as viewable content.\n                        Recognized image types: PNG, JPEG, GIF, WebP.\n                        \n                        Failed reads for individual files won't stop the entire operation.\n                        Only works within allowed directories.\n                        \n                        IMPORTANT: Always use absolute paths (starting with '/' or drive letter like 'C:\\') for reliability. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.\n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions.", "input_schema": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "move_file", "description": "\n                        Move or rename files and directories.\n                        \n                        Can move files between directories and rename them in a single operation.\n                        Both source and destination must be within allowed directories.\n                        \n                        IMPORTANT: Always use absolute paths (starting with '/' or drive letter like 'C:\\') for reliability. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.\n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions.", "input_schema": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "get_file_info", "description": "\n                        Retrieve detailed metadata about a file or directory including:\n                        - size\n                        - creation time\n                        - last modified time \n                        - permissions\n                        - type\n                        - lineCount (for text files)\n                        - lastLine (zero-indexed number of last line, for text files)\n                        - appendPosition (line number for appending, for text files)\n                        \n                        Only works within allowed directories.\n                        \n                        IMPORTANT: Always use absolute paths (starting with '/' or drive letter like 'C:\\') for reliability. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.\n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions.", "input_schema": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "execute_command", "description": "\n                        Execute a terminal command with timeout.\n                        \n                        Command will continue running in background if it doesn't complete within timeout.\n                        \n                        NOTE: For file operations, prefer specialized tools like read_file, search_code, \n                        list_directory instead of cat, grep, or ls commands.\n                        \n                        IMPORTANT: Always use absolute paths (starting with '/' or drive letter like 'C:\\') for reliability. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.\n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions.", "input_schema": {"type": "object", "properties": {"command": {"type": "string"}, "timeout_ms": {"type": "number"}, "shell": {"type": "string"}}, "required": ["command", "timeout_ms"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "read_output", "description": "\n                        Read new output from a running terminal session.\n                        Set timeout_ms for long running commands.\n                        \n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions.", "input_schema": {"type": "object", "properties": {"pid": {"type": "number"}, "timeout_ms": {"type": "number"}}, "required": ["pid"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "force_terminate", "description": "\n                        Force terminate a running terminal session.\n                        \n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions.", "input_schema": {"type": "object", "properties": {"pid": {"type": "number"}}, "required": ["pid"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "list_sessions", "description": "\n                        List all active terminal sessions.\n                        \n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions.", "input_schema": {"type": "object", "properties": {}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "list_processes", "description": "\n                        List all running processes.\n                        \n                        Returns process information including PID, command name, CPU usage, and memory usage.\n                        \n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions.", "input_schema": {"type": "object", "properties": {}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "kill_process", "description": "\n                        Terminate a running process by PID.\n                        \n                        Use with caution as this will forcefully terminate the specified process.\n                        \n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions.", "input_schema": {"type": "object", "properties": {"pid": {"type": "number"}}, "required": ["pid"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "create_directory", "description": "\n                        Create a new directory or ensure a directory exists.\n                        \n                        Can create multiple nested directories in one operation.\n                        Only works within allowed directories.\n                        \n                        IMPORTANT: Always use absolute paths (starting with '/' or drive letter like 'C:\\') for reliability. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.\n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions.", "input_schema": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {"properties": {"url": {"type": "string", "description": "url link", "title": "url"}}}, "annotations": null}, {"name": "write_file", "description": "\n                        Write or append to file contents. \n\n                        🎯 CHUNKING IS STANDARD PRACTICE: Always write files in chunks of 25-30 lines maximum.\n                        This is the normal, recommended way to write files - not an emergency measure.\n\n                        STANDARD PROCESS FOR ANY FILE:\n                        1. FIRST → write_file(filePath, firstChunk, {mode: 'rewrite'})  [≤30 lines]\n                        2. THEN → write_file(filePath, secondChunk, {mode: 'append'})   [≤30 lines]\n                        3. CONTINUE → write_file(filePath, nextChunk, {mode: 'append'}) [≤30 lines]\n\n                        ⚠️ ALWAYS CHUNK PROACTIVELY - don't wait for performance warnings!\n\n                        WHEN TO CHUNK (always be proactive):\n                        1. Any file expected to be longer than 25-30 lines\n                        2. When writing multiple files in sequence\n                        3. When creating documentation, code files, or configuration files\n                        \n                        HANDLING CONTINUATION (\"Continue\" prompts):\n                        If user asks to \"Continue\" after an incomplete operation:\n                        1. Read the file to see what was successfully written\n                        2. Continue writing ONLY the remaining content using {mode: 'append'}\n                        3. Keep chunks to 25-30 lines each\n                        \n                        Files over 50 lines will generate performance notes but are still written successfully.\n                        Only works within allowed directories.\n                        \n                        IMPORTANT: Always use absolute paths (starting with '/' or drive letter like 'C:\\') for reliability. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.\n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions.", "input_schema": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}, "mode": {"type": "string", "enum": ["rewrite", "append"], "default": "rewrite"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {"properties": {"p1": {"type": "array", "description": "sample1", "title": "p1"}, "p2": {"type": "array", "description": "sample2", "title": "p2"}}}, "annotations": null}, {"name": "edit_block", "description": "\n                        Apply surgical text replacements to files.\n                        \n                        BEST PRACTICE: Make multiple small, focused edits rather than one large edit.\n                        Each edit_block call should change only what needs to be changed - include just enough \n                        context to uniquely identify the text being modified.\n                        \n                        Takes:\n                        - file_path: Path to the file to edit\n                        - old_string: Text to replace\n                        - new_string: Replacement text\n                        - expected_replacements: Optional parameter for number of replacements\n                        \n                        By default, replaces only ONE occurrence of the search text.\n                        To replace multiple occurrences, provide the expected_replacements parameter with\n                        the exact number of matches expected.\n                        \n                        UNIQUENESS REQUIREMENT: When expected_replacements=1 (default), include the minimal\n                        amount of context necessary (typically 1-3 lines) before and after the change point,\n                        with exact whitespace and indentation.\n                        \n                        When editing multiple sections, make separate edit_block calls for each distinct change\n                        rather than one large replacement.\n                        \n                        When a close but non-exact match is found, a character-level diff is shown in the format:\n                        common_prefix{-removed-}{+added+}common_suffix to help you identify what's different.\n                        \n                        Similar to write_file, there is a configurable line limit (fileWriteLineLimit) that warns\n                        if the edited file exceeds this limit. If this happens, consider breaking your edits into\n                        smaller, more focused changes.\n                        \n                        IMPORTANT: Always use absolute paths (starting with '/' or drive letter like 'C:\\') for reliability. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.\n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions.", "input_schema": {"type": "object", "properties": {"file_path": {"type": "string"}, "old_string": {"type": "string"}, "new_string": {"type": "string"}, "expected_replacements": {"type": "number", "default": 1}}, "required": ["file_path", "old_string", "new_string"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {}, "annotations": null}, {"name": "set_config_value", "description": "\n                        Set a specific configuration value by key.\n                        \n                        WARNING: Should be used in a separate chat from file operations and \n                        command execution to prevent security issues.\n                        \n                        Config keys include:\n                        - blockedCommands (array)\n                        - defaultShell (string)\n                        - allowedDirectories (array of paths)\n                        - fileReadLineLimit (number, max lines for read_file)\n                        - fileWriteLineLimit (number, max lines per write_file call)\n                        - telemetryEnabled (boolean)\n                        \n                        IMPORTANT: Setting allowedDirectories to an empty array ([]) allows full access \n                        to the entire file system, regardless of the operating system.\n                        \n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions.", "input_schema": {"type": "object", "properties": {"key": {"type": "string"}, "value": {}}, "required": ["key"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {"properties": {"gvhvv": {"type": "string", "description": "hjjhjbv", "title": "gvhvv"}}}, "annotations": null}, {"name": "list_directory", "description": "\n                        Get a detailed listing of all files and directories in a specified path.\n                        \n                        Use this instead of 'execute_command' with ls/dir commands.\n                        Results distinguish between files and directories with [FILE] and [DIR] prefixes.\n                        Only works within allowed directories.\n                        \n                        IMPORTANT: Always use absolute paths (starting with '/' or drive letter like 'C:\\') for reliability. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.\n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions.", "input_schema": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {"properties": {"hbjbh": {"type": "string", "description": "vghjvhj", "title": "hbjbh"}}}, "annotations": null}, {"name": "search_files", "description": "\n                        Finds files by name using a case-insensitive substring matching.\n                        \n                        Use this instead of 'execute_command' with find/dir/ls for locating files.\n                        Searches through all subdirectories from the starting path.\n                        \n                        Has a default timeout of 30 seconds which can be customized using the timeoutMs parameter.\n                        Only searches within allowed directories.\n                        \n                        IMPORTANT: Always use absolute paths (starting with '/' or drive letter like 'C:\\') for reliability. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.\n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions.", "input_schema": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "timeoutMs": {"type": "number"}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {"properties": {"hgvvhg": {"type": "string", "description": "hjbhj", "title": "hgvvhg"}}}, "annotations": null}, {"name": "search_code", "description": "\n                        Search for text/code patterns within file contents using ripgrep.\n                        \n                        Use this instead of 'execute_command' with grep/find for searching code content.\n                        Fast and powerful search similar to VS Code search functionality.\n                        \n                        Supports regular expressions, file pattern filtering, and context lines.\n                        Has a default timeout of 30 seconds which can be customized.\n                        Only searches within allowed directories.\n                        \n                        IMPORTANT: Always use absolute paths (starting with '/' or drive letter like 'C:\\') for reliability. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.\n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions.", "input_schema": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "filePattern": {"type": "string"}, "ignoreCase": {"type": "boolean"}, "maxResults": {"type": "number"}, "includeHidden": {"type": "boolean"}, "contextLines": {"type": "number"}, "timeoutMs": {"type": "number"}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {"properties": {"hjvhj": {"type": "string", "description": "gvvjg", "title": "hjvhj"}}}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": "DesktopCommanderMCP", "git_user_name": "wonderwhy-er", "integrations": null, "url": null}}