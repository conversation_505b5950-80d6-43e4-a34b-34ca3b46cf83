{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "748a8221-d7d9-4352-93ae-00700f4d28b1", "name": "script-generation-mcp", "logo": null, "description": "generate video script", "category": "marketing", "tags": ["video", "auomation", "script"], "created_at": "2025-06-15T10:38:35.635904", "updated_at": "2025-08-11T14:40:09.066178", "owner_id": "fce79072-a235-4127-ac5b-b5b1709a8077", "hosted_url": "https://script-generation-mcp-dev-624209391722.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["script_generate", "research"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "script_generate", "description": "Provide topic and keyword to generator <PERSON><PERSON><PERSON>", "input_schema": {"$defs": {"Keywords": {"properties": {"time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Time"}, "objective": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Objective"}, "audience": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Audience"}, "gender": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Gender"}, "tone": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON>"}, "speakers": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Speakers"}}, "title": "Keywords", "type": "object"}, "ScriptType": {"enum": ["VIDEO", "TOPIC", "SCRIPT", "BLOG", "AI"], "title": "ScriptType", "type": "string"}, "VideoType": {"enum": ["SHORT", "LONG"], "title": "VideoType", "type": "string"}}, "properties": {"topic": {"title": "Topic", "type": "string"}, "script_type": {"$ref": "#/$defs/ScriptType", "default": "TOPIC"}, "keywords": {"$ref": "#/$defs/Keywords"}, "video_type": {"$ref": "#/$defs/VideoType", "default": "SHORT"}, "link": {"anyOf": [{"format": "uri", "maxLength": 2083, "minLength": 1, "type": "string"}, {"type": "null"}], "default": null, "title": "Link"}}, "required": ["topic"], "title": "GenerateScriptInput", "type": "object"}, "output_schema": {"properties": {"title": {"type": "string", "description": "Title of the generated script", "title": "title"}, "script": {"type": "string", "description": "The generated script", "title": "script"}, "script_type": {"type": "string", "description": "Type of the script", "title": "script_type"}, "video_type": {"type": "string", "description": "The type of video", "title": "video_type"}, "link": {"type": "string", "format": "uri", "description": "Optional link for the script", "title": "link"}}}, "annotations": null}, {"name": "research", "description": "Research for the given topic", "input_schema": {"properties": {"topic": {"title": "Topic", "type": "string"}}, "required": ["topic"], "title": "ResearchInput", "type": "object"}, "output_schema": null, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": null, "url": null}}