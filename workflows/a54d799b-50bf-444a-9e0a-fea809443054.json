{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "a54d799b-50bf-444a-9e0a-fea809443054", "name": "SDR Email Reply Workflow (trigger)", "description": "SDR_Email_Reply_Workflow_(trigger)", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/f744e0b3-6c85-4435-9dcd-5190fa3a1774.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/fbcd9522-5e54-4dd3-96f0-b58eee718ca9.json", "start_nodes": [{"field": "main_input", "type": "dict", "transition_id": "transition-MergeDataComponent-1755077432929"}, {"field": "from_email_address", "type": "string", "transition_id": "transition-MCP_SDR_Management_reply_email_from_customer-1755145274911"}, {"field": "to_email_address", "type": "string", "transition_id": "transition-MCP_SDR_Management_reply_email_from_customer-1755145274911"}, {"field": "subject", "type": "string", "transition_id": "transition-MCP_SDR_Management_reply_email_from_customer-1755145274911"}, {"field": "body", "type": "string", "transition_id": "transition-MCP_SDR_Management_reply_email_from_customer-1755145274911"}], "owner_id": "14902289-d9a4-43ec-8a31-989babf03fe3", "owner_name": "<PERSON><PERSON><PERSON><PERSON>", "use_count": 9, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-08-13T09:41:02.789280", "updated_at": "2025-08-22T10:06:09.469444", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1755077432929", "label": "test node", "integrations": []}, {"name": "MCP_SDR_Management_reply_email_from_customer", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "transition_id": "transition-MCP_SDR_Management_reply_email_from_customer-1755145274911", "type": "mcp", "display_name": "SDR Management - reply_email_from_customer", "label": "SDR Management - reply_email_from_customer", "integrations": null, "data": {"input_schema": {"properties": {"from_email_address": {"title": "From Email Address", "type": "string"}, "to_email_address": {"title": "To Email Address", "type": "string"}, "subject": {"title": "Subject", "type": "string"}, "body": {"title": "Body", "type": "string"}}, "required": ["from_email_address", "to_email_address", "subject", "body"], "title": "reply_email_from_customerArguments", "type": "object"}, "output_schema": null}}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1755062160395", "label": "Select Customer ID", "integrations": []}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-ND2NJGeD0u3W-0lwrwd-P", "label": "Select campaign_id", "integrations": []}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1755001254842", "label": "Select Email", "integrations": []}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1755000112383", "label": "Select user_id", "integrations": []}, {"name": "MCP_SDR_Management_get_campaign", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "transition_id": "transition-MCP_SDR_Management_get_campaign-1754999423499", "type": "mcp", "display_name": "SDR Management - get_campaign", "label": "SDR Management - get_campaign", "integrations": null, "data": {"input_schema": {"properties": {"campaign_id": {"title": "Campaign Id", "type": "string"}, "user_id": {"title": "User Id", "type": "string"}}, "required": ["campaign_id", "user_id"], "title": "get_campaignArguments", "type": "object"}, "output_schema": null}}, {"name": "MCP_SDR_Management_get_email_conversations", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "transition_id": "transition-MCP_SDR_Management_get_email_conversations-1755088479307", "type": "mcp", "display_name": "SDR Management - get_email_conversations", "label": "SDR Management - get_email_conversations", "integrations": null, "data": {"input_schema": {"properties": {"user_id": {"title": "User Id", "type": "string"}, "campaign_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Campaign Id"}, "customer_email": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Customer <PERSON><PERSON>"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Is Active"}, "limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Limit"}, "offset": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Offset"}}, "required": ["user_id"], "title": "get_email_conversationsArguments", "type": "object"}, "output_schema": null}}, {"name": "MCP_SDR_Management_list_products", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "transition_id": "transition-MCP_SDR_Management_list_products-1754999683570", "type": "mcp", "display_name": "SDR Management - list_products", "label": "SDR Management - list_products", "integrations": null, "data": {"input_schema": {"properties": {"user_id": {"title": "User Id", "type": "string"}, "campaign_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Campaign Id"}, "limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Limit"}, "offset": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Offset"}}, "required": ["user_id"], "title": "list_productsArguments", "type": "object"}, "output_schema": null}}, {"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1755000222785", "label": "Merge Data", "integrations": []}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1754974314883", "label": "<PERSON>a Researcher"}, {"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1755145814522", "label": "Merge Data", "integrations": []}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-3i-y1ttLJH6-b8tcEOXDw", "label": "Email Drafter"}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-UniversalConverterComponent-1755086018385", "label": "Universal Converter", "integrations": []}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1754979793796", "label": "Select Body", "integrations": []}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1754979792333", "label": "Select Subject", "integrations": []}, {"name": "MCP_SDR_Management_create_email_conversation", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "transition_id": "transition-MCP_SDR_Management_create_email_conversation-1755088135307", "type": "mcp", "display_name": "SDR Management - create_email_conversation", "label": "create_email_conversation", "integrations": null, "data": {"input_schema": {"properties": {"user_id": {"title": "User Id", "type": "string"}, "campaign_id": {"title": "Campaign Id", "type": "string"}, "customer_id": {"title": "Customer Id", "type": "string"}, "subject": {"title": "Subject", "type": "string"}, "body": {"title": "Body", "type": "string"}, "sender_name": {"title": "Sender Name", "type": "string"}}, "required": ["user_id", "campaign_id", "customer_id", "subject", "body", "sender_name"], "title": "create_email_conversationArguments", "type": "object"}, "output_schema": null}}, {"name": "MCP_Gmail_send_email", "id": "37db65ab-0586-434e-a58d-7ddc6d9a8beb", "transition_id": "transition-MCP_Gmail_send_email-1754978300299", "type": "mcp", "display_name": "Gmail - send_email", "label": "Send Mail to Lead", "integrations": ["20cebfff-1435-4081-90df-90a149f41194"], "data": {"input_schema": {"properties": {"to": {"title": "To", "type": "string"}, "subject": {"title": "Subject", "type": "string"}, "body": {"title": "Body", "type": "string"}, "cc": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Cc"}, "bcc": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Bcc"}, "html": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Html"}}, "required": ["to", "subject", "body"], "title": "SendEmail", "type": "object"}, "output_schema": {"properties": {"body": {"type": "string", "description": "body", "title": "body"}}}}}], "source_workflow_id": "ff312cb7-b9ff-41a4-9472-717a6df7d90d", "source_version_id": "35dcc9e0-ec3b-4388-9f19-c7570ad3e601", "has_updates": false, "current_version_id": "35dcc9e0-ec3b-4388-9f19-c7570ad3e601"}}