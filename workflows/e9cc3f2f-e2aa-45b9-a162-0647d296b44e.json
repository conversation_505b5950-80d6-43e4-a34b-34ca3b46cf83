{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "e9cc3f2f-e2aa-45b9-a162-0647d296b44e", "name": "SDR Contact Generation Flow", "description": "SDR_Contact_Generation_Flow", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/260d3707-d526-4d5a-b13d-0a83ff056deb.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/c1f48af4-7f51-4f4e-86f3-a6e2ee710b2e.json", "start_nodes": [{"field": "main_input", "type": "dict", "transition_id": "transition-MergeDataComponent-1755070220281"}, {"field": "input_data", "type": "multiline", "transition_id": "transition-ConditionalNode-1755017361324"}], "owner_id": "14902289-d9a4-43ec-8a31-989babf03fe3", "owner_name": "<PERSON><PERSON><PERSON><PERSON>", "use_count": 6, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-08-13T09:29:23.430770", "updated_at": "2025-08-21T13:02:49.109869", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "ConditionalNode", "display_name": "Switch-Case Router", "type": "component", "transition_id": "transition-ConditionalNode-1755017361324", "label": "Switch-Case Router", "integrations": []}, {"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1755070220281", "label": "Merge Data", "integrations": []}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1755061201677", "label": "Select Campaign ID", "integrations": []}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1755061197460", "label": "Select User ID", "integrations": []}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1755017021182", "label": "Select Email", "integrations": []}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1755017012281", "label": "Select First Name", "integrations": []}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1755017009282", "label": "Select Last Name", "integrations": []}, {"name": "MCP_SDR_Management_get_campaign", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "transition_id": "transition-MCP_SDR_Management_get_campaign-1755009517312", "type": "mcp", "display_name": "SDR Management - get_campaign", "label": "SDR Management - get_campaign", "integrations": null, "data": {"input_schema": {"properties": {"campaign_id": {"title": "Campaign Id", "type": "string"}, "user_id": {"title": "User Id", "type": "string"}}, "required": ["campaign_id", "user_id"], "title": "get_campaignArguments", "type": "object"}, "output_schema": null}}, {"name": "MCP_SDR_Management_create_customers", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "transition_id": "transition-uPMyS9L6JswgnxJNIqpxE", "type": "mcp", "display_name": "SDR Management - create_customers", "label": "SDR Management - create_customers (Copy)", "integrations": null, "data": {"input_schema": {"properties": {"customers": {"items": {"additionalProperties": true, "type": "object"}, "title": "Customers", "type": "array"}, "user_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "User Id"}, "campaign_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Campaign Id"}, "tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Tags"}}, "required": ["customers"], "title": "create_customersArguments", "type": "object"}, "output_schema": null}}, {"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1755061085766", "label": "Merge Data", "integrations": []}, {"name": "MCP_Apollo_IO_people_enrichment", "id": "2e2c611f-9338-4193-a416-e332e1a32267", "transition_id": "transition-MCP_Apollo_IO_people_enrichment-1755007435635", "type": "mcp", "display_name": "Apollo IO - people_enrichment", "label": "Enrich Customer Data", "integrations": null, "data": {"input_schema": {"type": "object", "properties": {"first_name": {"type": "string", "description": "Person's first name"}, "last_name": {"type": "string", "description": "Person's last name"}, "email": {"type": "string", "description": "Person's email address"}, "domain": {"type": "string", "description": "Company domain"}, "organization_name": {"type": "string", "description": "Organization name"}, "linkedin_url": {"type": "string", "description": "Person's LinkedIn profile URL"}}}, "output_schema": null}}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1755007782703", "label": "Customer List Agent"}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-UniversalConverterComponent-1755076579023", "label": "Universal Converter", "integrations": []}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1755010055751", "label": "Select q_organization_domains_list", "integrations": []}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1755010065200", "label": "Select person_seniorities", "integrations": []}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1755010061249", "label": "Select person_titles", "integrations": []}, {"name": "MCP_Apollo_IO_people_search", "id": "2e2c611f-9338-4193-a416-e332e1a32267", "transition_id": "transition-MCP_Apollo_IO_people_search-1755009669179", "type": "mcp", "display_name": "Apollo IO - people_search", "label": "Search Customers", "integrations": null, "data": {"input_schema": {"type": "object", "properties": {"q_organization_domains_list": {"type": "array", "items": {"type": "string"}, "description": "List of organization domains to search within"}, "person_titles": {"type": "array", "items": {"type": "string"}, "description": "List of job titles to search for"}, "person_seniorities": {"type": "array", "items": {"type": "string"}, "description": "List of seniority levels to search for"}}}, "output_schema": null}}, {"name": "MCP_SDR_Management_create_customers", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "transition_id": "transition-MCP_SDR_Management_create_customers-1755005233928", "type": "mcp", "display_name": "SDR Management - create_customers", "label": "SDR Management - create_customers", "integrations": null, "data": {"input_schema": {"properties": {"customers": {"items": {"additionalProperties": true, "type": "object"}, "title": "Customers", "type": "array"}, "user_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "User Id"}, "campaign_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Campaign Id"}, "tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Tags"}}, "required": ["customers"], "title": "create_customersArguments", "type": "object"}, "output_schema": null}}], "source_workflow_id": "141192fc-05ec-488a-a7a1-24243d6eec97", "source_version_id": "d1dcc2d2-6b01-4639-a5be-1762e6481d7b", "has_updates": false, "current_version_id": "d1dcc2d2-6b01-4639-a5be-1762e6481d7b"}}