{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "2a215231-bcff-4741-84b4-b91a1e781abd", "name": "Email Draft workflow", "description": "Email_Draft_workflow", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/ca61c808-0ef3-423b-b3f6-0823c1c61750.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/882f7add-d791-4993-960b-60de8c7c60b8.json", "start_nodes": [{"field": "to", "type": "string", "transition_id": "transition-MCP_Gmail_create_draft-1754036922757"}], "owner_id": "8222552d-2264-459d-9cec-4f16d693d714", "owner_name": "<PERSON><PERSON><PERSON> ", "use_count": 9, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-08-01T12:15:19.521895", "updated_at": "2025-08-31T07:18:24.414796", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "MCP_Gmail_create_draft", "id": "37db65ab-0586-434e-a58d-7ddc6d9a8beb", "transition_id": "transition-MCP_Gmail_create_draft-1754036922757", "type": "mcp", "display_name": "Gmail - create_draft", "label": "Gmail - create_draft", "data": {"input_schema": {"properties": {"to": {"title": "To", "type": "string"}, "subject": {"title": "Subject", "type": "string"}, "body": {"title": "Body", "type": "string"}, "cc": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Cc"}, "bcc": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Bcc"}, "html": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Html"}}, "required": ["to", "subject", "body"], "title": "CreateDraft", "type": "object"}, "output_schema": {}}}], "source_workflow_id": "16298f61-de3e-491d-9043-a518fd9045a0", "source_version_id": "41c85397-572c-40d8-bac5-5edf3c04ac80", "has_updates": false, "current_version_id": "41c85397-572c-40d8-bac5-5edf3c04ac80"}}