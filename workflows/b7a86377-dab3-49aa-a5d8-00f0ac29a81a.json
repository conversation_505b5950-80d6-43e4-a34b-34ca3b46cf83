{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "b7a86377-dab3-49aa-a5d8-00f0ac29a81a", "name": "Profile Research Workflow", "description": "Profile_Research_Workflow", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/4a877ba7-1500-47fb-8eb2-acb80c5bccaf.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/4ed4f87c-1298-4b8c-823c-64f3fb3b8dbc.json", "start_nodes": [{"field": "iteration_list", "type": "list", "transition_id": "transition-LoopNode-1752762417794"}], "owner_id": "d3b78018-fe5a-4dd4-8148-d533ab3d8a93", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 6, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-18T07:53:54.317487", "updated_at": "2025-08-22T04:56:30.276593", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1752763020242", "label": "Merge Data"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1752767219980", "label": "AI Agent Executor"}, {"name": "MCP_Google_Document_append_document", "id": "0931e5d9-fccc-459b-81a5-c1a251d16c7a", "transition_id": "transition-MCP_Google_Document_append_document-1753272096997", "type": "mcp", "display_name": "Google Document - append_document", "label": "Google Document - append_document", "data": {"input_schema": {"properties": {"document_id": {"title": "Document Id", "type": "string"}, "content": {"title": "Content", "type": "string"}}, "required": ["document_id", "content"], "title": "AppendDocument", "type": "object"}, "output_schema": {"profile": ""}}}], "source_workflow_id": "3c17973e-646f-48a8-b84c-eb65f37646b5", "source_version_id": "6342f08b-7ef8-4dd3-9c52-513053785f54", "has_updates": false, "current_version_id": "6342f08b-7ef8-4dd3-9c52-513053785f54"}}