{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "92244fba-e6db-47ea-ab4e-eb7aae829af1", "name": "Untitled Workflow", "description": "Untitled_Workflow", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/5e952703-329c-4d63-a0fc-e88ab4096065.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/e4a65100-5725-4ca0-ac09-667247bccfdd.json", "start_nodes": [{"field": "query", "type": "string", "transition_id": "transition-AgenticAI-1752214388208"}], "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07", "owner_name": "<PERSON><PERSON>", "use_count": 0, "execution_count": 0, "average_rating": null, "category": "general", "tags": null, "version": "1.0.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-08-28T11:50:56.359272", "updated_at": "2025-08-28T11:50:56.359279", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1752214388208", "label": "AI Agent Executor"}], "source_workflow_id": "ae463cbe-d575-4df8-a077-23c926210679", "source_version_id": "e23b1401-3f09-443e-9f94-12358ed07fe7", "has_updates": false, "current_version_id": "e23b1401-3f09-443e-9f94-12358ed07fe7"}}