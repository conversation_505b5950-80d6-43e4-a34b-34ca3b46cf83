{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "f907eb17-76f9-4583-80be-6c6bfa757fc6", "name": "GET JDs (Agent-Chat)", "description": "GET_JDs_(Agent-Cha<PERSON>)", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/0a9a0fe9-92b3-45eb-9fab-4bf4758a7518.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/eaf2c64d-1b49-49d6-9d37-89b04c090f28.json", "start_nodes": [{"field": "query", "type": "string", "transition_id": "transition-AgenticAI-1753962094088"}], "owner_id": "598f7395-c39a-4bfa-9996-0e3bff89047c", "owner_name": "<PERSON><PERSON><PERSON><PERSON>", "use_count": 4, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.2.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-31T11:48:59.588161", "updated_at": "2025-08-22T04:49:38.195110", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1753962094088", "label": "Final Response"}], "source_workflow_id": "536ae035-d01f-4dbd-aa47-47f60aad0b7e", "source_version_id": "2c9372a8-a733-4bee-9d8b-86521120ff11", "has_updates": false, "current_version_id": "2c9372a8-a733-4bee-9d8b-86521120ff11"}}