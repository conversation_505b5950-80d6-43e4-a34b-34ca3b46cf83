{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "4fdde5aa-6911-4bda-8123-f94e36e3afed", "name": "Website Generator", "description": "Website_Generator", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/3efeb724-2bef-4062-b54c-81077f7410e0.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/308ccf60-3ae0-4250-98a0-d80449b67ef4.json", "start_nodes": [{"field": "query", "type": "string", "transition_id": "transition-AgenticAI-1751525393128"}], "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07", "owner_name": "<PERSON><PERSON>", "use_count": 21, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.2.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-03T13:34:26.316896", "updated_at": "2025-08-25T11:56:24.901012", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1751525393128", "label": "Context-Creator"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1751525493675", "label": "Code-Setup"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1751525602170", "label": "Layout-Definer"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1751526273887", "label": "Create-File,"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1751526449966", "label": "Code-<PERSON><PERSON>er"}], "source_workflow_id": "607f95c5-87f7-4335-9e32-039f8fa223c7", "source_version_id": "dd0bb2e6-c465-4018-8cf3-fd49c19865a5", "has_updates": false, "current_version_id": "dd0bb2e6-c465-4018-8cf3-fd49c19865a5"}}