{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "9ee4badd-3963-40b6-a59e-572f3c829980", "name": "PPT Generation", "description": "PPT_Generation", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/ae97cb34-979e-4c9b-9e2a-6d43c93d73ca.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/c2865dd2-829d-47a0-952f-27026dbce23f.json", "start_nodes": [{"field": "query", "type": "string", "transition_id": "transition-AgenticAI-1751462692955"}], "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07", "owner_name": "<PERSON><PERSON>", "use_count": 17, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.2.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-01T14:14:34.537508", "updated_at": "2025-08-26T06:09:54.194157", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1751462692955", "label": "Presentation-Content-Architect"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1751375257553", "label": "Presentation-Template-Selector"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1751376276838", "label": "Slide-Content-Formatter"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1751376474982", "label": "Slide-Deck-Generator"}], "source_workflow_id": "ce24a72a-201e-4f58-922c-dcdd0621bd31", "source_version_id": "509c06fd-522a-4545-8e38-cf82bff87aa4", "has_updates": false, "current_version_id": "509c06fd-522a-4545-8e38-cf82bff87aa4"}}