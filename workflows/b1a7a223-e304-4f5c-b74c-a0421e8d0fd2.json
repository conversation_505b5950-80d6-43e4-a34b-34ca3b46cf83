{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "b1a7a223-e304-4f5c-b74c-a0421e8d0fd2", "name": "Screening (<PERSON>)", "description": "Screening_(<PERSON>_<PERSON><PERSON>)", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/6370bf59-5b7a-4d5b-817c-e7175fdc74e6.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/3ca8872c-dde9-4746-b862-ac93a2949c12.json", "start_nodes": [{"field": "query", "type": "string", "transition_id": "transition-AgenticAI-1753435722849"}, {"field": "main_input", "type": "string", "transition_id": "transition-CombineTextComponent-1753780149499"}], "owner_id": "55b1d790-e922-40ec-9da9-7f784894ab42", "owner_name": "<PERSON><PERSON>", "use_count": 6, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-08-04T11:01:47.253053", "updated_at": "2025-09-01T10:10:00.452143", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1753435722849", "label": "AI Agent Executor"}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-CombineTextComponent-1753780149499", "label": "Combine Text"}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-UniversalConverterComponent-1753436327179", "label": "Universal Converter"}, {"name": "MCP_Google_Sheets_count_column_values", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-MCP_Google_Sheets_count_column_values-1753719458434", "type": "mcp", "display_name": "Google Sheets - count_column_values", "label": "Google Sheets - count_column_values", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "column": {"title": "Column", "type": "string"}, "value": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Value"}, "include_empty": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Include Empty"}}, "required": ["spreadsheet_id", "column"], "title": "CountCol<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "object"}, "output_schema": {}}}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753436877482", "label": "Select Data"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753436148415", "label": "Select Data"}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-CombineTextComponent-1753376942805", "label": "Cell ID"}, {"name": "MCP_Google_Sheets_set_formula", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-aIM1V1Yz3KzCZn9FxFWuk", "type": "mcp", "display_name": "Google Sheets - set_formula", "label": "Google Sheets - set_formula (Copy)", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "cell": {"title": "Cell", "type": "string"}, "formula": {"title": "Formula", "type": "string"}}, "required": ["spreadsheet_id", "cell", "formula"], "title": "SetForm<PERSON>", "type": "object"}, "output_schema": {}}}], "source_workflow_id": "982cf744-c763-428e-a0b5-aa1e762bb33d", "source_version_id": "fd668e8f-f354-40cb-989b-2c8436f55e2f", "has_updates": false, "current_version_id": "fd668e8f-f354-40cb-989b-2c8436f55e2f"}}