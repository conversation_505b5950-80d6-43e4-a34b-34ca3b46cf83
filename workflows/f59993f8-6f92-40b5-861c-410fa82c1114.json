{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "f59993f8-6f92-40b5-861c-410fa82c1114", "name": "Research Workflow", "description": "Research_Workflow", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/fbbf21e8-170c-4203-bfba-f37fec9882b8.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/bffc4b99-c7fe-42d6-a05e-477242b96e58.json", "start_nodes": [{"field": "query", "type": "string", "transition_id": "transition-AgenticAI-1752769698362"}], "owner_id": "d3b78018-fe5a-4dd4-8148-d533ab3d8a93", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 4, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-18T07:54:01.436086", "updated_at": "2025-08-22T04:56:18.950204", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1752769698362", "label": "Degree (1) Information Gathering"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1752759749407", "label": "Employee Profile LinkedIn URL Extractor"}, {"name": "MCP_Google_Document_append_document", "id": "0931e5d9-fccc-459b-81a5-c1a251d16c7a", "transition_id": "transition-csboc9SEboVbjfhxSl5Jg", "type": "mcp", "display_name": "Google Document - append_document", "label": "Research Document (1)", "data": {"input_schema": {"properties": {"document_id": {"title": "Document Id", "type": "string"}, "content": {"title": "Content", "type": "string"}}, "required": ["document_id", "content"], "title": "AppendDocument", "type": "object"}, "output_schema": {"properties": {"generated_string_output": {"type": "string", "description": "the value which is created from tavily", "title": "generated_string_output"}}}}}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1752769760904", "label": "Company LinkedIn URL Extractor"}, {"name": "workflow-3c17973e-646f-48a8-b84c-eb65f37646b5", "display_name": "Profile Research Workflow", "type": "component", "transition_id": "transition-workflow-3c17973e-646f-48a8-b84c-eb65f37646b5-1752769769373", "label": "Profile Research Workflow"}, {"name": "workflow-d3ec861d-da29-4e5e-8c8e-db480d15d5cd", "display_name": "Company Research Workflow", "type": "component", "transition_id": "transition-workflow-d3ec861d-da29-4e5e-8c8e-db480d15d5cd-1752771947660", "label": "Company Research Workflow"}, {"name": "MCP_Google_Document_append_document", "id": "0931e5d9-fccc-459b-81a5-c1a251d16c7a", "transition_id": "transition-KrjNaiampJJYOtlz1OtQK", "type": "mcp", "display_name": "Google Document - append_document", "label": "Research Document 3", "data": {"input_schema": {"properties": {"document_id": {"title": "Document Id", "type": "string"}, "content": {"title": "Content", "type": "string"}}, "required": ["document_id", "content"], "title": "AppendDocument", "type": "object"}, "output_schema": {"properties": {"generated_string_output": {"type": "string", "description": "the value which is created from tavily", "title": "generated_string_output"}}}}}], "source_workflow_id": "cf07c857-008c-44a7-b164-07f40cee8461", "source_version_id": "d6d679e2-f42c-4870-b62b-86a4a24a6f85", "has_updates": false, "current_version_id": "d6d679e2-f42c-4870-b62b-86a4a24a6f85"}}