{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "51729580-e6a7-42b8-843a-fbf0d4a71466", "name": "Google Sheets Row Range Reader v2", "description": "No description provided.", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/d4421b59-6265-4d06-8ef2-e6b48e280ed3.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/1f6e8cbd-f1d6-4244-a0d0-3c47874953dd.json", "start_nodes": null, "owner_id": "8da56b93-b442-4166-a8a1-b902bd38f363", "owner_name": "<PERSON><PERSON>", "use_count": 5, "execution_count": 0, "average_rating": null, "category": "general", "tags": null, "version": "1.0.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-30T06:25:56.314809", "updated_at": "2025-08-22T04:50:51.098979", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1753422353677", "label": "Merge Data After Receiving Input"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753427589329", "label": "Select Range Key From Input"}, {"name": "MCP_Google_Sheets_get_values_in_range", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-MCP_Google_Sheets_get_values_in_range-1753423079288", "type": "mcp", "display_name": "Google Sheets - get_values_in_range", "label": "Google Sheets - get_values_in_range", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "range_notation": {"title": "Range Notation", "type": "string"}, "value_render_option": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "FORMATTED_VALUE", "title": "Value Render Option"}}, "required": ["spreadsheet_id", "range_notation"], "title": "GetValuesInRange", "type": "object"}, "output_schema": {}}}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753445895323", "label": "Select Data"}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-UniversalConverterComponent-1753445333499", "label": "Universal Converter"}], "source_workflow_id": "aef04e3c-3549-45c7-97fe-1edb68c095a5", "source_version_id": "27b8e541-8a22-4e76-91b3-89a85b1cc30f", "has_updates": false, "current_version_id": "27b8e541-8a22-4e76-91b3-89a85b1cc30f"}}