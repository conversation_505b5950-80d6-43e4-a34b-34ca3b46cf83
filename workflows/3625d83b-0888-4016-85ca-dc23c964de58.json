{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "3625d83b-0888-4016-85ca-dc23c964de58", "name": "Screening", "description": "Screening", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/16b34d58-34af-4acb-a07c-4ec95fc8ac29.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/c5e29efc-5738-4e4c-b84d-116456243e6d.json", "start_nodes": [{"field": "input_variables", "type": "dict", "transition_id": "transition-AgenticAI-1753435722849"}, {"field": "main_input", "type": "string", "transition_id": "transition-CombineTextComponent-1753780149499"}], "owner_id": "55b1d790-e922-40ec-9da9-7f784894ab42", "owner_name": "<PERSON><PERSON>", "use_count": 5, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.4.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-29T06:45:02.347839", "updated_at": "2025-09-01T10:09:41.877863", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1753435722849", "label": "AI Agent Executor"}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-CombineTextComponent-1753780149499", "label": "Combine Text", "integrations": []}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-UniversalConverterComponent-1753436327179", "label": "Universal Converter", "integrations": []}, {"name": "MCP_Google_Sheets_count_column_values", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-MCP_Google_Sheets_count_column_values-1753719458434", "type": "mcp", "display_name": "Google Sheets - count_column_values", "label": "Google Sheets - count_column_values", "integrations": [], "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "column": {"title": "Column", "type": "string"}, "value": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Value"}, "include_empty": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Include Empty"}}, "required": ["spreadsheet_id", "column"], "title": "CountCol<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "object"}, "output_schema": {}}}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753436148415", "label": "Select Data", "integrations": []}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753436877482", "label": "Select Data", "integrations": []}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-CombineTextComponent-1753376942805", "label": "Cell ID", "integrations": []}, {"name": "MCP_Google_Sheets_set_formula", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-aIM1V1Yz3KzCZn9FxFWuk", "type": "mcp", "display_name": "Google Sheets - set_formula", "label": "Google Sheets - set_formula (Copy)", "integrations": [], "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "cell": {"title": "Cell", "type": "string"}, "formula": {"title": "Formula", "type": "string"}}, "required": ["spreadsheet_id", "cell", "formula"], "title": "SetForm<PERSON>", "type": "object"}, "output_schema": {}}}], "source_workflow_id": "130c2319-4032-4992-9278-0b9d100b177f", "source_version_id": "5ad80901-6a95-4c03-96e0-6797a0dcd837", "has_updates": false, "current_version_id": "5ad80901-6a95-4c03-96e0-6797a0dcd837"}}