{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "fcfa7933-d5fb-4c9c-bcd0-a22254da694d", "name": "Meta ads Create", "description": "Meta_ads_Create", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/2179a182-9b13-4ea2-9555-7118ec6f35c1.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/0b74fb0b-18bc-46be-bd1b-8de866b1cee6.json", "start_nodes": [{"field": "name", "type": "string", "transition_id": "transition-MCP_MetaAds_create_meta_campaign-1752936013134"}, {"field": "objective", "type": "string", "transition_id": "transition-MCP_MetaAds_create_meta_campaign-1752936013134"}, {"field": "buying_type", "type": "string", "transition_id": "transition-MCP_MetaAds_create_meta_campaign-1752936013134"}, {"field": "daily_budget", "type": "int", "transition_id": "transition-MCP_MetaAds_create_meta_campaign-1752936013134"}, {"field": "name", "type": "string", "transition_id": "transition-MCP_MetaAds_create_adset-1753166235171"}, {"field": "daily_budget", "type": "int", "transition_id": "transition-MCP_MetaAds_create_adset-1753166235171"}, {"field": "countries", "type": "array", "transition_id": "transition-MCP_MetaAds_create_adset-1753166235171"}, {"field": "publisher_platforms", "type": "array", "transition_id": "transition-MCP_MetaAds_create_adset-1753166235171"}, {"field": "facebook_positions", "type": "array", "transition_id": "transition-MCP_MetaAds_create_adset-1753166235171"}, {"field": "optimization_goal", "type": "string", "transition_id": "transition-MCP_MetaAds_create_adset-1753166235171"}, {"field": "billing_event", "type": "string", "transition_id": "transition-MCP_MetaAds_create_adset-1753166235171"}, {"field": "bid_strategy", "type": "string", "transition_id": "transition-MCP_MetaAds_create_adset-1753166235171"}, {"field": "image_url", "type": "string", "transition_id": "transition-MCP_MetaAds_upload_ad_image-1753166529868"}, {"field": "name", "type": "string", "transition_id": "transition-MCP_MetaAds_create_ad_creative-*************"}, {"field": "page_id", "type": "string", "transition_id": "transition-MCP_MetaAds_create_ad_creative-*************"}, {"field": "link", "type": "string", "transition_id": "transition-MCP_MetaAds_create_ad_creative-*************"}, {"field": "message", "type": "string", "transition_id": "transition-MCP_MetaAds_create_ad_creative-*************"}, {"field": "image_name", "type": "string", "transition_id": "transition-MCP_MetaAds_create_ad_creative-*************"}, {"field": "description", "type": "string", "transition_id": "transition-MCP_MetaAds_create_ad_creative-*************"}, {"field": "call_to_action", "type": "object", "transition_id": "MCP_MetaAds_create_ad_creative-*************", "properties": [{"field": "type", "type": "string", "transition_id": "MCP_MetaAds_create_ad_creative-*************"}]}, {"field": "name", "type": "string", "transition_id": "transition-MCP_MetaAds_create_ad-1753330607929"}], "owner_id": "5229e05b-aaea-4850-9972-7268559318cf", "owner_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "use_count": 6, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.11.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-17T11:21:48.182521", "updated_at": "2025-08-22T05:00:04.887332", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "MCP_MetaAds_upload_ad_image", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "transition_id": "transition-MCP_MetaAds_upload_ad_image-1753166529868", "type": "mcp", "display_name": "MetaAds - upload_ad_image", "label": "MetaAds - upload_ad_image", "data": {"input_schema": {"description": "Schema for uploading an ad image", "properties": {"image_url": {"description": "Image file URL for upload", "title": "Image Url", "type": "string"}}, "required": ["image_url"], "title": "UploadAdImageRequest", "type": "object"}, "output_schema": {"content": [{"type": "text", "text": {"success": true, "data": {"image_hash": "9aaf3efc9ffaef5ce3bd7d89522323e0"}}}], "isError": false}}}, {"name": "MCP_MetaAds_create_meta_campaign", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "transition_id": "transition-MCP_MetaAds_create_meta_campaign-1752936013134", "type": "mcp", "display_name": "MetaAds - create_meta_campaign", "label": "MetaAds - create_meta_campaign", "data": {"input_schema": {"$defs": {"MetaBidStrategy": {"description": "Meta Ads Bidding Strategies - Updated 2024+", "enum": ["LOWEST_COST_WITHOUT_CAP", "LOWEST_COST_WITH_BID_CAP", "LOWEST_COST_WITH_MIN_ROAS"], "title": "MetaBidStrategy", "type": "string"}, "MetaCampaignObjective": {"description": "Meta Ads Campaign Objectives - Updated to match Meta API 2024+", "enum": ["OUTCOME_AWARENESS", "OUTCOME_ENGAGEMENT", "OUTCOME_LEADS", "OUTCOME_SALES", "OUTCOME_TRAFFIC", "OUTCOME_APP_PROMOTION", "BRAND_AWARENESS", "LINK_CLICKS", "POST_ENGAGEMENT", "LEAD_GENERATION", "APP_INSTALLS", "CONVERSIONS"], "title": "MetaCampaignObjective", "type": "string"}, "MetaCampaignStatus": {"description": "Meta Ads Campaign Status", "enum": ["ACTIVE", "PAUSED", "DELETED", "ARCHIVED"], "title": "MetaCampaignStatus", "type": "string"}}, "description": "Schema for creating a Meta Ads Campaign", "properties": {"name": {"description": "Campaign name", "title": "Name", "type": "string"}, "objective": {"$ref": "#/$defs/MetaCampaignObjective", "description": "Campaign objective"}, "status": {"$ref": "#/$defs/MetaCampaignStatus", "default": "PAUSED", "description": "Campaign status"}, "buying_type": {"default": "AUCTION", "description": "Buying type for the campaign", "title": "Buying Type", "type": "string"}, "bid_strategy": {"anyOf": [{"$ref": "#/$defs/MetaBidStrategy"}, {"type": "null"}], "default": null, "description": "Bidding strategy"}, "daily_budget": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "Daily budget in cents", "title": "Daily Budget"}, "lifetime_budget": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "Lifetime budget in cents", "title": "Lifetime Budget"}, "start_time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Campaign start time (ISO format)", "title": "Start Time"}, "end_time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Campaign end time (ISO format)", "title": "End Time"}, "special_ad_categories": {"description": "Special ad categories (required by Meta API)", "items": {"type": "string"}, "title": "Special Ad Categories", "type": "array"}}, "required": ["name", "objective"], "title": "MetaCampaignCreate", "type": "object"}, "output_schema": {"content": [{"type": "text", "text": {"message": "Meta Ads campaign created successfully!", "campaign_id": "120227424229350727", "campaign_name": "test", "objective": "OUTCOME_AWARENESS", "status": "PAUSED", "daily_budget": "Not set", "success": true}}], "isError": false}}}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-uNGLPzWWgPeR4OwM775JP", "label": "Upload image Response"}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-UniversalConverterComponent-1752999701996", "label": "Create Campaign Response"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1752999778926", "label": "Create Campaign"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-2uz7RUfowK2Xk_2nSK8x8", "label": "uploads image"}, {"name": "MCP_MetaAds_create_adset", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "transition_id": "transition-MCP_MetaAds_create_adset-1753166235171", "type": "mcp", "display_name": "MetaAds - create_adset", "label": "MetaAds - create_adset", "data": {"input_schema": {"description": "<PERSON><PERSON><PERSON> for creating an ad set", "properties": {"campaign_id": {"description": "Meta Ads campaign ID this ad set belongs to", "title": "Campaign Id", "type": "string"}, "name": {"description": "Ad set name", "title": "Name", "type": "string"}, "status": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "PAUSED", "description": "Initial ad set status", "title": "Status"}, "daily_budget": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "Daily budget in cents", "title": "Daily Budget"}, "lifetime_budget": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "Lifetime budget in cents", "title": "Lifetime Budget"}, "countries": {"description": "List of target countries (e.g., ['US', 'CA'])", "items": {}, "title": "Countries", "type": "array"}, "publisher_platforms": {"description": "List of publisher platforms (e.g., ['facebook', 'instagram'])", "items": {}, "title": "Publisher Platforms", "type": "array"}, "facebook_positions": {"description": "List of Facebook positions (e.g., ['feed', 'right_hand_column'])", "items": {}, "title": "Facebook Positions", "type": "array"}, "optimization_goal": {"description": "Conversion optimization goal (e.g., 'LINK_CLICKS')", "title": "Optimization Goal", "type": "string"}, "billing_event": {"description": "How you're charged (e.g., 'IMPRESSIONS')", "title": "Billing Event", "type": "string"}, "bid_amount": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "Bid amount in account currency (in cents)", "title": "<PERSON><PERSON>"}, "bid_strategy": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Bid strategy (e.g., 'LOWEST_COST')", "title": "Bid Strategy"}, "start_time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Start time (ISO 8601)", "title": "Start Time"}, "end_time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "End time (ISO 8601)", "title": "End Time"}}, "required": ["campaign_id", "name", "countries", "publisher_platforms", "facebook_positions", "optimization_goal", "billing_event"], "title": "CreateAdSetRequest", "type": "object"}, "output_schema": {"content": [{"type": "text", "text": {"success": true, "data": {"id": "120230046788450565"}}}], "isError": false}}}, {"name": "MCP_MetaAds_create_ad_creative", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "transition_id": "transition-MCP_MetaAds_create_ad_creative-*************", "type": "mcp", "display_name": "MetaAds - create_ad_creative", "label": "Create ad creative", "data": {"input_schema": {"description": "<PERSON><PERSON><PERSON> for creating an ad creative", "example": {"call_to_action": {"type": "LEARN_MORE"}, "description": "Sleek. Powerful. Easy to Use.", "image_hash": "<uploaded_image_hash>", "link": "<landing-page-link>", "message": "This is the primary text for the ad. Discover why our new product is changing the game for everyone!", "name": "The Best New Product is Here!", "page_id": "<page_id>"}, "properties": {"name": {"description": "Creative name", "title": "Name", "type": "string"}, "page_id": {"description": "Page ID for the ad creative", "title": "Page Id", "type": "string"}, "image_hash": {"description": "Image hash for the ad creative", "title": "Image Hash", "type": "string"}, "link": {"description": "Landing page URL for the ad creative", "title": "Link", "type": "string"}, "message": {"description": "Primary text for the ad creative", "title": "Message", "type": "string"}, "image_name": {"description": "Name of the image", "title": "Image Name", "type": "string"}, "description": {"description": "Description of the ad creative", "title": "Description", "type": "string"}, "call_to_action": {"additionalProperties": true, "description": "Call to action for the ad creative", "title": "Call To Action", "type": "object"}}, "required": ["name", "page_id", "image_hash", "link", "message", "image_name", "description", "call_to_action"], "title": "CreateAdCreativeRequest", "type": "object"}, "output_schema": {"content": [{"type": "text", "text": {"success": true, "data": {"id": "779030998020141"}}}], "isError": false}}}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-rJEG4us5Kqv_7Jy9co-Qw", "label": "Create Adsets Response"}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-7CACeBI73h54aOEFjoCl-", "label": "Ads Creative"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition--3M8z6m5wdxm1kfvVf2fp", "label": "Ads Creative"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-F9W28Cn2xeo1h4bBJj4_t", "label": "Create Adsets"}, {"name": "MCP_MetaAds_create_ad", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "transition_id": "transition-MCP_MetaAds_create_ad-1753330607929", "type": "mcp", "display_name": "MetaAds - create_ad", "label": "MetaAds - create_ad", "data": {"input_schema": {"description": "<PERSON><PERSON><PERSON> for creating an ad", "example": {"adset_id": "<adset_id>", "creative_id": "<creative_id>", "name": "Ad 1", "status": "PAUSED"}, "properties": {"name": {"description": "Ad name", "title": "Name", "type": "string"}, "adset_id": {"description": "Ad set ID where this ad will be placed", "title": "Adset Id", "type": "string"}, "creative_id": {"description": "Creative ID to be used for this ad", "title": "Creative Id", "type": "string"}, "status": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "PAUSED", "description": "Initial ad status", "title": "Status"}, "bid_amount": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "Optional bid amount (in cents)", "title": "<PERSON><PERSON>"}, "tracking_specs": {"anyOf": [{"items": {"additionalProperties": true, "type": "object"}, "type": "array"}, {"type": "null"}], "default": null, "description": "Optional tracking specifications", "title": "Tracking Specs"}}, "required": ["name", "adset_id", "creative_id"], "title": "CreateAdRequest", "type": "object"}, "output_schema": {}}}], "source_workflow_id": "b8049d59-dd53-4cce-8310-069bc3c2e015", "source_version_id": "19562531-cbcc-401f-88b9-27bf746aeed8", "has_updates": false, "current_version_id": "19562531-cbcc-401f-88b9-27bf746aeed8"}}