{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "c75340d5-60d5-4e3a-93f0-b7d829693817", "name": "SpurRidge Workflow", "description": "SpurRidge_Workflow", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/8c8abc12-5a8e-4f1f-aad9-5dc53c43c257.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/a6d34cdb-a623-41d4-af1f-a1f9a1cd3051.json", "start_nodes": [{"field": "query", "type": "string", "transition_id": "transition-AgenticAI-1752233311066"}], "owner_id": "d3b78018-fe5a-4dd4-8148-d533ab3d8a93", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 4, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-18T07:54:10.495147", "updated_at": "2025-08-22T04:56:08.312469", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1752233311066", "label": "Investment Thesis Parser"}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-UniversalConverterComponent-1752640608512", "label": "Document Converter"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1752650554256", "label": "Data Extractor"}, {"name": "workflow-cf07c857-008c-44a7-b164-07f40cee8461", "display_name": "Research Workflow", "type": "component", "transition_id": "transition-workflow-cf07c857-008c-44a7-b164-07f40cee8461-1752822652216", "label": "Research Workflow"}], "source_workflow_id": "0f3a676f-e5e8-4e86-8809-afe431658a12", "source_version_id": "fd00e783-16e1-435b-938c-b1ff9cb67031", "has_updates": false, "current_version_id": "fd00e783-16e1-435b-938c-b1ff9cb67031"}}